from pydantic_settings import BaseSettings
from typing import Optional

class DatabaseSettings(BaseSettings):
    """تنظیمات اتصال به دیتابیس"""
    
    url: str = "postgresql://postgres:Honey33454@localhost:5432/stock_data"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 1800
    connect_timeout: int = 10

    class Config:
        env_prefix = "DB_"
        case_sensitive = False