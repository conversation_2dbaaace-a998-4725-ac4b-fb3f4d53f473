import finpy_tse as fpy

# Fetch the market stock list
stock_list = fpy.Build_Market_StockList(
    bourse=True,
    farabourse=True,
    payeh=True,
    detailed_list=True,
    show_progress=True,
    save_excel=True,
    save_csv=True,
    save_path='D:/FinPy-TSE Data/'
)

# Ensure stock_list is not None before proceeding
if stock_list:
    # Fetch historical price data for the stock list
    fpy.Build_PricePanel(
        stock_list,
        jalali_date=True,
        save_excel=True,
        save_path='D:/FinPy-TSE Data/Price Panel/'
    )
else:
    print("❌ Failed to fetch the market stock list.")
