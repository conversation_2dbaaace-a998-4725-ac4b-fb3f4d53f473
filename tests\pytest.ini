[pytest]
addopts = -v -ra -q --durations=3
markers =
    integration: marks tests as integration tests
    critical: marks tests as critical/essential tests
    health: marks tests as health checks
python_functions = test_*
testpaths = tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore:.*class-based.*config.*:DeprecationWarning
    ignore::Warning:pydantic.*
    ignore:Support for class-based.*:Warning

