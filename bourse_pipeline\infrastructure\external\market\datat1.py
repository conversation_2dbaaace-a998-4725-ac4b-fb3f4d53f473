import sys
import asyncio
import time
import random
import finpy_tse as fpy
import pandas as pd

# تنظیم event loop برای ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

def get_daily_price_history(stock, start_date, end_date):
    """
    دریافت تاریخچه قیمت‌های روزانه یک سهم
    """
    try:
        print(f"در حال دریافت تاریخچه قیمت روزانه سهم '{stock}' ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_Price_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            adjust_price=True,
            show_weekday=False,
            double_date=False
        )
        print(f"تاریخچه قیمت سهم '{stock}' دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت تاریخچه قیمت سهم '{stock}': {ex}")
        return None

def get_ri_history(stock, start_date, end_date):
    """
    دریافت اطلاعات حقیقی-حقوقی یک سهم
    """
    try:
        print(f"در حال دریافت اطلاعات حقیقی-حقوقی سهم '{stock}' ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_RI_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            show_weekday=False,
            double_date=False,
            alt=False
        )
        print(f"اطلاعات حقیقی-حقوقی سهم '{stock}' دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت اطلاعات حقیقی-حقوقی سهم '{stock}': {ex}")
        return None

def get_intraday_trades(stock, start_date, end_date):
    """
    دریافت ریز معاملات (Intraday Trades) یک سهم
    """
    try:
        print(f"در حال دریافت ریز معاملات سهم '{stock}' ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_IntradayTrades_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        print(f"ریز معاملات سهم '{stock}' دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت ریز معاملات سهم '{stock}': {ex}")
        return None

def get_intraday_orderbook(stock, date):
    """
    دریافت عمق بازار (Order Book) یک سهم برای یک تاریخ مشخص
    """
    try:
        print(f"در حال دریافت عمق بازار سهم '{stock}' برای تاریخ {date} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_IntradayOB_History(
            stock=stock,
            start_date=date,
            end_date=date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        print(f"عمق بازار سهم '{stock}' دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت عمق بازار سهم '{stock}': {ex}")
        return None

def get_queue_history(stock, start_date, end_date):
    """
    دریافت وضعیت صف معاملات یک سهم
    """
    try:
        print(f"در حال دریافت وضعیت صف معاملات سهم '{stock}' ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_Queue_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            show_per_capita=True,
            show_weekday=False,
            double_date=False,
            show_progress=True
        )
        print(f"وضعیت صف معاملات سهم '{stock}' دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت وضعیت صف معاملات سهم '{stock}': {ex}")
        return None

def get_market_indexes(start_date, end_date):
    """
    دریافت شاخص کل بازار (CWI) به عنوان نمونه از شاخص‌های بازار
    """
    try:
        print("در حال دریافت شاخص کل بازار (CWI) ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_CWI_History(
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            just_adj_close=False,
            show_weekday=False,
            double_date=False
        )
        print("شاخص کل بازار دریافت شد.")
        return df
    except Exception as ex:
        print(f"خطا در دریافت شاخص کل بازار: {ex}")
        return None

def main():
    # نمونه نماد برای دریافت داده؛ در صورت نیاز می‌توانید این مقدار را تغییر دهید یا لیستی از سهام دریافت کنید.
    stock = "خودرو"  
    start_date = '1400-01-01'
    end_date = '1401-01-01'
    single_date = '1400-08-01'  # برای دریافت عمق بازار، یک تاریخ مشخص

    # دریافت تاریخچه قیمت‌های روزانه
    daily_df = get_daily_price_history(stock, start_date, end_date)
    if daily_df is not None:
        daily_df.to_csv("daily_price_history.csv", index=False, encoding="utf-8-sig")

    # دریافت اطلاعات حقیقی-حقوقی
    ri_df = get_ri_history(stock, start_date, end_date)
    if ri_df is not None:
        ri_df.to_csv("ri_history.csv", index=False, encoding="utf-8-sig")

    # دریافت داده‌های درون‌روز (ریز معاملات)
    intraday_trades_df = get_intraday_trades(stock, start_date, end_date)
    if intraday_trades_df is not None:
        intraday_trades_df.to_csv("intraday_trades.csv", index=False, encoding="utf-8-sig")

    # دریافت عمق بازار (داده‌های درون‌روز - order book)
    intraday_ob_df = get_intraday_orderbook(stock, single_date)
    if intraday_ob_df is not None:
        intraday_ob_df.to_csv("intraday_orderbook.csv", index=False, encoding="utf-8-sig")

    # دریافت وضعیت صف معاملات
    queue_df = get_queue_history(stock, start_date, end_date)
    if queue_df is not None:
        queue_df.to_csv("queue_history.csv", index=False, encoding="utf-8-sig")

    # دریافت شاخص‌های بازار (به عنوان نمونه شاخص کل بازار)
    market_indexes_df = get_market_indexes(start_date, end_date)
    if market_indexes_df is not None:
        market_indexes_df.to_csv("market_indexes.csv", index=False, encoding="utf-8-sig")

    print("\nتمام داده‌ها با موفقیت دریافت و در فایل‌های CSV ذخیره شدند.")

if __name__ == "__main__":
    main()
