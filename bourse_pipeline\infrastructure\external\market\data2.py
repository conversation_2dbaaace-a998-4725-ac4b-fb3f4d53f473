import os
import sys
import asyncio
import time
import random
import pandas as pd
import finpy_tse as fpy

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# ایجاد پوشه data جهت ذخیره فایل‌های خروجی (در صورت عدم وجود)
os.makedirs("data", exist_ok=True)

def get_all_symbols():
    """
    دریافت لیست تمام سهام بازار بورس ایران
    """
    try:
        print("در حال دریافت لیست تمام سهام از بازار ...")
        # دریافت لیست جامع سهام با استفاده از Build_Market_StockList
        stock_list = fpy.Build_Market_StockList(
            bourse=True,
            farabourse=True,
            payeh=True,
            detailed_list=True,
            show_progress=True,
            save_csv=False
        )
        print("ستون‌های موجود در لیست سهام:", stock_list.columns.tolist())
        # تعیین ستون مناسب برای نماد
        if 'نماد' in stock_list.columns:
            symbol_col = 'نماد'
        elif 'Name' in stock_list.columns:
            symbol_col = 'Name'
        elif 'Ticker(4)' in stock_list.columns:
            symbol_col = 'Ticker(4)'
        else:
            symbol_col = stock_list.columns[0]
        symbols = stock_list[symbol_col].tolist()
        print(f"{len(symbols)} نماد دریافت شد.")
        return symbols
    except Exception as ex:
        print("خطا در دریافت لیست نمادها:", ex)
        return []

def get_daily_price_history(stock, start_date, end_date):
    """
    دریافت تاریخچه قیمت‌های روزانه یک سهم
    """
    try:
        print(f"دریافت تاریخچه قیمت روزانه برای {stock} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_Price_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            adjust_price=True,
            show_weekday=False,
            double_date=False
        )
        if df is not None and not df.empty:
            file_path = f"data/daily_price_{stock}.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ تاریخچه قیمت {stock} در {file_path} ذخیره شد.")
        else:
            print(f"⚠️ تاریخچه قیمت {stock} دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت تاریخچه قیمت {stock}: {ex}")

def get_ri_history(stock, start_date, end_date):
    """
    دریافت اطلاعات حقیقی-حقوقی یک سهم
    """
    try:
        print(f"دریافت اطلاعات حقیقی-حقوقی برای {stock} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_RI_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            show_weekday=False,
            double_date=False,
            alt=False
        )
        if df is not None and not df.empty:
            file_path = f"data/ri_history_{stock}.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ اطلاعات حقیقی-حقوقی {stock} در {file_path} ذخیره شد.")
        else:
            print(f"⚠️ اطلاعات حقیقی-حقوقی {stock} دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت اطلاعات حقیقی-حقوقی {stock}: {ex}")

def get_intraday_trades(stock, start_date, end_date):
    """
    دریافت ریز معاملات (Intraday Trades) یک سهم
    """
    try:
        print(f"دریافت ریز معاملات (Intraday Trades) برای {stock} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_IntradayTrades_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        if df is not None and not df.empty:
            file_path = f"data/intraday_trades_{stock}.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ ریز معاملات {stock} در {file_path} ذخیره شد.")
        else:
            print(f"⚠️ ریز معاملات {stock} دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت ریز معاملات {stock}: {ex}")

def get_intraday_orderbook(stock, date):
    """
    دریافت عمق بازار (Order Book) یک سهم برای تاریخ مشخص
    """
    try:
        print(f"دریافت عمق بازار (Order Book) برای {stock} در تاریخ {date} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_IntradayOB_History(
            stock=stock,
            start_date=date,
            end_date=date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        if df is not None and not df.empty:
            file_path = f"data/intraday_orderbook_{stock}_{date}.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ عمق بازار {stock} در {file_path} ذخیره شد.")
        else:
            print(f"⚠️ عمق بازار {stock} دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت عمق بازار {stock}: {ex}")

def get_queue_history(stock, start_date, end_date):
    """
    دریافت وضعیت صف معاملات یک سهم
    """
    try:
        print(f"دریافت وضعیت صف معاملات برای {stock} ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_Queue_History(
            stock=stock,
            start_date=start_date,
            end_date=end_date,
            show_per_capita=True,
            show_weekday=False,
            double_date=False,
            show_progress=True
        )
        if df is not None and not df.empty:
            file_path = f"data/queue_history_{stock}.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ وضعیت صف معاملات {stock} در {file_path} ذخیره شد.")
        else:
            print(f"⚠️ وضعیت صف معاملات {stock} دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت وضعیت صف معاملات {stock}: {ex}")

def get_market_indexes(start_date, end_date):
    """
    دریافت شاخص کل بازار (CWI)
    """
    try:
        print("دریافت شاخص کل بازار (CWI) ...")
        time.sleep(random.uniform(1, 3))
        df = fpy.Get_CWI_History(
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            just_adj_close=False,
            show_weekday=False,
            double_date=False
        )
        if df is not None and not df.empty:
            file_path = "data/market_indexes.csv"
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            print(f"✅ شاخص کل بازار در {file_path} ذخیره شد.")
        else:
            print("⚠️ شاخص کل بازار دریافت نشد.")
    except Exception as ex:
        print(f"❌ خطا در دریافت شاخص کل بازار: {ex}")

def main():
    # تعیین محدوده تاریخی
    start_date = '1400-01-01'
    end_date = '1401-01-01'
    orderbook_date = '1400-08-01'  # تاریخ ثابت برای دریافت عمق بازار

    # دریافت لیست تمام نمادها
    symbols = get_all_symbols()
    if not symbols:
        print("⚠️ لیست نمادها دریافت نشد. اجرای برنامه متوقف می‌شود.")
        return

    total = len(symbols)
    for idx, stock in enumerate(symbols, start=1):
        print(f"\n========== {idx}/{total}: پردازش {stock} ==========")
        get_daily_price_history(stock, start_date, end_date)
        get_ri_history(stock, start_date, end_date)
        get_intraday_trades(stock, start_date, end_date)
        get_intraday_orderbook(stock, orderbook_date)
        get_queue_history(stock, start_date, end_date)
        print(f"---------- پایان پردازش {stock} ----------\n")
    
    # دریافت شاخص کل بازار به‌عنوان داده کلی
    get_market_indexes(start_date, end_date)
    print("\n🎯 تمام داده‌ها دریافت و در پوشه 'data' ذخیره شدند.")

if __name__ == "__main__":
    main()
