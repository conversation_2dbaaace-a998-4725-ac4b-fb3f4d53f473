name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10']
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
          
      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          
      - name: Configure Poetry
        run: |
          poetry config virtualenvs.create true
          poetry config virtualenvs.in-project true
          
      - name: Install dependencies
        run: poetry install --no-interaction --no-root
        
      - name: Run unit tests
        run: poetry run pytest -v -m "unit"
        
      - name: Run integration tests
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
        run: poetry run pytest -v -m "integration" --run-integration
