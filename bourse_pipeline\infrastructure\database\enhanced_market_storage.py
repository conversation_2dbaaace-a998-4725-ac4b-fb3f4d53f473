import pandas as pd
import json
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class EnhancedMarketStorage:
    """کلاس بهبود یافته برای ذخیره داده‌های بازار در پایگاه داده"""
    
    def __init__(self, db_url: str):
        """
        مقداردهی اولیه کلاس
        
        Args:
            db_url: آدرس اتصال به پایگاه داده
        """
        self.engine = create_engine(db_url)
        self._ensure_tables_exist()
    
    def _ensure_tables_exist(self):
        """اطمینان از وجود جداول مورد نیاز در پایگاه داده"""
        try:
            with self.engine.connect() as conn:
                # جدول داده‌های ساختاریافته بازار
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS market_data (
                        id SERIAL PRIMARY KEY,
                        symbol VARCHAR(30) NOT NULL,
                        date DATE NOT NULL,
                        open DECIMAL(20,2),
                        high DECIMAL(20,2),
                        low DECIMAL(20,2),
                        close DECIMAL(20,2),
                        adj_close DECIMAL(20,2),
                        volume BIGINT,
                        value DECIMAL(20,2),
                        trades_count INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(symbol, date)
                    );
                """))
                
                # جدول داده‌های خام بازار (به صورت JSON)
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS market_raw_data (
                        id SERIAL PRIMARY KEY,
                        symbol VARCHAR(50) NOT NULL,
                        raw_data JSONB NOT NULL,
                        extraction_date TIMESTAMP NOT NULL,
                        data_type VARCHAR(50) NOT NULL
                    );
                """))
                
                # ایجاد ایندکس‌ها
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_market_data_symbol_date ON market_data(symbol, date);"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_market_data_date ON market_data(date);"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_raw_data_symbol ON market_raw_data(symbol);"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_raw_data_type ON market_raw_data(data_type);"))
                
                logger.info("✅ جداول و ایندکس‌ها با موفقیت بررسی و ایجاد شدند.")
        except SQLAlchemyError as e:
            logger.error(f"❌ خطا در ایجاد جداول: {e}")
            raise
    
    def save_market_data(self, data_dict: Dict[str, pd.DataFrame]):
        """
        ذخیره دیکشنری داده‌های بازار در جدول market_data
        
        Args:
            data_dict: دیکشنری داده‌های بازار (کلید: نماد، مقدار: DataFrame)
        """
        try:
            total_records = 0
            for symbol, df in data_dict.items():
                if df is None or df.empty:
                    logger.warning(f"⚠️ داده‌های {symbol} خالی است و ذخیره نمی‌شود.")
                    continue
                
                # اطمینان از وجود ستون‌های مورد نیاز
                required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
                if not all(col in df.columns for col in required_columns):
                    logger.warning(f"⚠️ داده‌های {symbol} فاقد ستون‌های ضروری است: {required_columns}")
                    continue
                
                # تغییر نام ستون‌ها به حروف کوچک برای سازگاری با پایگاه داده
                df_copy = df.copy()
                df_copy.columns = [col.lower() for col in df_copy.columns]
                
                # اضافه کردن ستون symbol
                df_copy['symbol'] = symbol
                
                # تبدیل ستون date به datetime اگر نیاز باشد
                if 'date' in df_copy.columns and not pd.api.types.is_datetime64_any_dtype(df_copy['date']):
                    df_copy['date'] = pd.to_datetime(df_copy['date'])
                
                # ذخیره در پایگاه داده
                df_copy.to_sql('market_data', self.engine, if_exists='append', index=False, 
                              method='multi', chunksize=1000)
                
                total_records += len(df_copy)
                logger.info(f"✅ داده‌های {symbol} با {len(df_copy)} رکورد ذخیره شد")
            
            logger.info(f"✅ در مجموع {total_records} رکورد برای {len(data_dict)} نماد ذخیره شد")
            return total_records
        except Exception as e:
            logger.error(f"❌ خطا در ذخیره داده‌ها: {str(e)}")
            raise
    
    def save_raw_data(self, raw_data: Dict[str, Union[Dict, pd.DataFrame]], extraction_date: Optional[datetime] = None):
        """
        ذخیره داده‌های خام در جدول market_raw_data
        
        Args:
            raw_data: دیکشنری داده‌های خام (کلید: نوع داده، مقدار: دیکشنری یا DataFrame)
            extraction_date: تاریخ استخراج داده‌ها (اگر None باشد، از زمان فعلی استفاده می‌شود)
        """
        if extraction_date is None:
            extraction_date = datetime.now()
            
        try:
            rows = []
            
            for data_type, data in raw_data.items():
                if isinstance(data, dict):
                    # دیکشنری از DataFrameها (مثل stock_prices)
                    for symbol, df in data.items():
                        if df is not None and not (isinstance(df, pd.DataFrame) and df.empty):
                            # تبدیل DataFrame به JSON
                            if isinstance(df, pd.DataFrame):
                                json_data = df.to_dict(orient='records')
                            else:
                                json_data = df
                                
                            rows.append({
                                'symbol': symbol,
                                'raw_data': json.dumps(json_data, ensure_ascii=False),
                                'extraction_date': extraction_date,
                                'data_type': data_type
                            })
                elif isinstance(data, pd.DataFrame) and not data.empty:
                    # DataFrame مستقیم (مثل market_indices)
                    rows.append({
                        'symbol': 'ALL',  # برای داده‌های کلی بازار
                        'raw_data': json.dumps(data.to_dict(orient='records'), ensure_ascii=False),
                        'extraction_date': extraction_date,
                        'data_type': data_type
                    })
            
            # درج داده‌ها به صورت دسته‌ای
            if rows:
                with self.engine.connect() as conn:
                    for row in rows:
                        conn.execute(
                            text("""
                                INSERT INTO market_raw_data 
                                (symbol, raw_data, extraction_date, data_type)
                                VALUES (:symbol, :raw_data::jsonb, :extraction_date, :data_type)
                            """),
                            row
                        )
                    conn.commit()
                
                logger.info(f"✅ {len(rows)} رکورد داده خام در پایگاه داده ذخیره شد")
                return len(rows)
            else:
                logger.warning("⚠️ هیچ داده خامی برای ذخیره وجود ندارد")
                return 0
                
        except Exception as e:
            logger.error(f"❌ خطا در ذخیره داده‌های خام: {str(e)}")
            raise
    
    def get_market_data(self, symbol: Optional[str] = None, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None) -> pd.DataFrame:
        """
        بازیابی داده‌های بازار از جدول market_data
        
        Args:
            symbol: نماد مورد نظر (اگر None باشد، همه نمادها بازیابی می‌شوند)
            start_date: تاریخ شروع (YYYY-MM-DD)
            end_date: تاریخ پایان (YYYY-MM-DD)
            
        Returns:
            DataFrame حاوی داده‌های بازار
        """
        query = "SELECT * FROM market_data WHERE 1=1"
        params = {}
        
        if symbol:
            query += " AND symbol = :symbol"
            params['symbol'] = symbol
            
        if start_date:
            query += " AND date >= :start_date"
            params['start_date'] = start_date
            
        if end_date:
            query += " AND date <= :end_date"
            params['end_date'] = end_date
            
        query += " ORDER BY symbol, date"
        
        try:
            return pd.read_sql(query, self.engine, params=params)
        except Exception as e:
            logger.error(f"❌ خطا در بازیابی داده‌های بازار: {str(e)}")
            raise
    
    def get_raw_data(self, data_type: Optional[str] = None, symbol: Optional[str] = None) -> List[Dict]:
        """
        بازیابی داده‌های خام از جدول market_raw_data
        
        Args:
            data_type: نوع داده (مثلاً 'stock_prices')
            symbol: نماد مورد نظر
            
        Returns:
            لیستی از دیکشنری‌های حاوی داده‌های خام
        """
        query = "SELECT * FROM market_raw_data WHERE 1=1"
        params = {}
        
        if data_type:
            query += " AND data_type = :data_type"
            params['data_type'] = data_type
            
        if symbol:
            query += " AND symbol = :symbol"
            params['symbol'] = symbol
            
        query += " ORDER BY extraction_date DESC"
        
        try:
            df = pd.read_sql(query, self.engine, params=params)
            if df.empty:
                return []
                
            # تبدیل ستون raw_data از JSON به دیکشنری
            results = []
            for _, row in df.iterrows():
                item = row.to_dict()
                item['raw_data'] = json.loads(item['raw_data'])
                results.append(item)
                
            return results
        except Exception as e:
            logger.error(f"❌ خطا در بازیابی داده‌های خام: {str(e)}")
            raise
