bourse-pipeline/


--------------------------------------------------------------------
bourse_pipeline/
├── application/     # لایه کاربردی - منطق کسب و کار
├── domain/         # هسته اصلی - مدل‌های دامنه
├── infrastructure/ # زیرساخت - پایگاه داده و سرویس‌های خارجی
├── interfaces/     # رابط‌های کاربری و API
├── utils/          # توابع کمکی
└── core/          # تنظیمات اصلی و اشتراک‌گذاری
---------------------------------------------------------------------------------
graph TD
    A[Data Ingestion] --> B[Preprocessor]
    B --> C[Technical Analysis]
    B --> D[ML Analysis]
    C --> E[Signal Generator]
    D --> E
    E --> F[Trading System]
    F --> G[Risk Manager]
    F --> H[Monitor]