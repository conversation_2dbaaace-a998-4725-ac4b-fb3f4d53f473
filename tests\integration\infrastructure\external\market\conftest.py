import pytest
import logging
from typing import Dict
from contextlib import contextmanager
import time
from datetime import datetime, timedelta
import finpy_tse as fpy
import pandas as pd

# تنظیمات لاگینگ برای تست‌ها
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@contextmanager
def timer(description: str) -> None:
    """زمان‌سنج برای اندازه‌گیری مدت اجرای توابع"""
    start = time.time()
    yield
    elapsed = time.time() - start
    logger.info(f"{description}: {elapsed:.2f} seconds")

@pytest.fixture(scope="session")
def performance_stats() -> Dict:
    """فیکسچر برای جمع‌آوری آمار عملکرد"""
    stats = {'durations': {}}
    yield stats
    logger.info("\n=== آمار عملکرد ===")
    for operation, duration in stats['durations'].items():
        logger.info(f"{operation}: {duration:.2f}s")

@pytest.fixture(scope="session")
def valid_test_symbols():
    """Get a list of valid symbols from market watch"""
    logger.info("\n=== Getting Valid Test Symbols ===")
    
    fallback_symbols = ["فولاد", "شستا"]
    
    try:
        market_watch = fpy.Get_MarketWatch(save_excel=False)
        if isinstance(market_watch, tuple):
            market_watch = market_watch[0]
        
        if not isinstance(market_watch, pd.DataFrame) or market_watch.empty:
            logger.warning("Market watch unavailable, using fallback symbols")
            return fallback_symbols
        
        # Filter for actively traded symbols
        active_symbols = market_watch[
            (market_watch['Volume'] > 0) &  # Has trading volume
            (market_watch['Close'] > 0)     # Has valid price
        ]
        
        if active_symbols.empty:
            logger.warning("No actively trading symbols found")
            return fallback_symbols
        
        # Get top symbols by value
        symbols = active_symbols.sort_values('Value', ascending=False).index[:3].tolist()
        
        logger.info(f"Selected active symbols: {symbols}")
        return symbols
        
    except Exception as e:
        logger.error(f"Error getting market symbols: {str(e)}")
        return fallback_symbols

@pytest.fixture(scope="session")
def valid_date_range():
    """Get a valid date range for testing"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    return {
        'start': start_date.strftime("%Y-%m-%d"),
        'end': end_date.strftime("%Y-%m-%d")
    }

@pytest.fixture(autouse=True)
def check_market_service(request):
    """Check market service availability before running integration tests"""
    if request.node.get_closest_marker('integration'):
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Checking market service availability (attempt {attempt + 1}/{max_retries})")
                market_watch = fpy.Get_MarketWatch(save_excel=False)
                if isinstance(market_watch, tuple):
                    market_watch = market_watch[0]
                    
                if not market_watch.empty:
                    logger.info("✅ Market service is available")
                    return
                    
                logger.warning("Market service returned empty data")
                
            except Exception as e:
                logger.error(f"Market service check failed: {e}")
            
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                
        pytest.skip("Market service is not accessible after multiple attempts")

