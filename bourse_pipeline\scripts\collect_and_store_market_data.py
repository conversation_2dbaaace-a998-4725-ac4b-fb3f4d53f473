import logging
import sys
import os
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional
import pandas as pd
import json
from dotenv import load_dotenv

# بارگذاری متغیرهای محیطی از فایل .env
load_dotenv()

# تنظیم مسیر برای واردسازی ماژول‌های پروژه
sys.path.append(str(Path(__file__).parent.parent.parent))

from bourse_pipeline.infrastructure.external.market.market_data import MarketDataCollector
from bourse_pipeline.infrastructure.database.enhanced_market_storage import EnhancedMarketStorage

# تنظیم لاگر
log_path = Path('logs')
log_path.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_path / 'market_data_collection.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """پردازش آرگومان‌های ورودی برنامه"""
    parser = argparse.ArgumentParser(description="دریافت و ذخیره داده‌های بازار سهام تهران")
    parser.add_argument(
        "--start-date",
        help="تاریخ شروع جمع‌آوری داده‌ها (YYYY-MM-DD)",
        type=str,
        default=(datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--end-date",
        help="تاریخ پایان جمع‌آوری داده‌ها (YYYY-MM-DD)",
        type=str,
        default=datetime.now().strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--output-dir",
        help="مسیر ذخیره داده‌های خروجی",
        type=str,
        default="data/market"
    )
    parser.add_argument(
        "--request-delay",
        help="تاخیر بین درخواست‌ها (ثانیه)",
        type=float,
        default=0.5
    )
    parser.add_argument(
        "--max-retries",
        help="حداکثر تعداد تلاش‌های مجدد",
        type=int,
        default=3
    )
    parser.add_argument(
        "--max-workers",
        help="حداکثر تعداد پردازش‌های همزمان",
        type=int,
        default=4
    )
    parser.add_argument(
        "--db-url",
        help="آدرس اتصال به دیتابیس",
        type=str,
        default=os.getenv("DATABASE_URL")
    )
    parser.add_argument(
        "--save-to-db",
        help="ذخیره داده‌ها در پایگاه داده",
        action="store_true",
        default=True
    )
    parser.add_argument(
        "--save-to-file",
        help="ذخیره داده‌ها در فایل",
        action="store_true",
        default=True
    )
    parser.add_argument(
        "--save-raw-data",
        help="ذخیره داده‌های خام در پایگاه داده",
        action="store_true",
        default=True
    )
    
    return parser.parse_args()

def validate_date(date_str: str) -> bool:
    """اعتبارسنجی فرمت تاریخ"""
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def ensure_directory(path: str) -> Path:
    """اطمینان از وجود دایرکتوری و ایجاد آن در صورت نیاز"""
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path

def convert_gregorian_to_jalali(date_str: str) -> str:
    """تبدیل تاریخ میلادی به شمسی"""
    import jdatetime
    g_date = datetime.strptime(date_str, "%Y-%m-%d")
    j_date = jdatetime.date.fromgregorian(date=g_date.date())
    return j_date.strftime("%Y-%m-%d")

def create_market_data_collector(config: Dict) -> MarketDataCollector:
    """ایجاد نمونه از کلاس MarketDataCollector"""
    return MarketDataCollector(config)

def collect_and_store_data(
    start_date: str,
    end_date: str,
    output_dir: str,
    request_delay: float,
    max_retries: int,
    max_workers: int,
    db_url: Optional[str] = None,
    save_to_db: bool = True,
    save_to_file: bool = True,
    save_raw_data: bool = True
) -> bool:
    """دریافت و ذخیره داده‌های بازار"""
    try:
        # تبدیل تاریخ‌های میلادی به شمسی (مورد نیاز finpy_tse)
        jalali_start_date = convert_gregorian_to_jalali(start_date)
        jalali_end_date = convert_gregorian_to_jalali(end_date)
        
        logger.info(f"🔄 شروع جمع‌آوری داده‌های بازار از {start_date} تا {end_date}")
        logger.info(f"🔄 تاریخ شمسی: از {jalali_start_date} تا {jalali_end_date}")
        
        # ایجاد مسیر خروجی
        if save_to_file:
            output_path = ensure_directory(output_dir)
        
        # تنظیمات برای MarketDataCollector
        config = {
            'data_ingestion': {
                'request_delay': request_delay,
                'batch_size': 2000,
                'max_workers': max_workers,
                'retry_attempts': max_retries,
                'human_simulation': {
                    'enabled': True,
                    'min_delay': 0.8,
                    'max_delay': 2.0,
                    'random_pause': True,
                    'pause_interval': 5,
                    'pause_duration': [2, 5]
                }
            }
        }
        
        # ایجاد نمونه از کلاس جمع‌کننده داده
        collector = create_market_data_collector(config)
        
        # جمع‌آوری داده‌ها
        raw_data = collector.get_raw_market_data(jalali_start_date, jalali_end_date)
        
        if not raw_data:
            logger.error("❌ هیچ داده‌ای دریافت نشد")
            return False
            
        # نمایش خلاصه داده‌های جمع‌آوری شده
        logger.info("\n📊 خلاصه داده‌های دریافت شده:")
        for data_type, data in raw_data.items():
            if isinstance(data, dict):
                logger.info(f"  - {data_type}: {len(data)} نماد")
            elif isinstance(data, pd.DataFrame):
                logger.info(f"  - {data_type}: {len(data)} رکورد")
            else:
                logger.info(f"  - {data_type}: {type(data)}")

        # ذخیره در فایل
        if save_to_file:
            collector.save_data(raw_data, str(output_path))
            logger.info(f"✅ داده‌ها در مسیر {output_path} ذخیره شدند")

        # ذخیره در دیتابیس
        if save_to_db and db_url:
            storage = EnhancedMarketStorage(db_url)
            
            # ذخیره داده‌های قیمت در جدول ساختاریافته
            stock_prices = raw_data.get('stock_prices', {})
            if stock_prices:
                records_count = storage.save_market_data(stock_prices)
                logger.info(f"✅ {records_count} رکورد داده قیمت در جدول market_data ذخیره شد")
            
            # ذخیره داده‌های خام در جدول JSON
            if save_raw_data:
                raw_records_count = storage.save_raw_data(raw_data)
                logger.info(f"✅ {raw_records_count} رکورد داده خام در جدول market_raw_data ذخیره شد")

        return True

    except Exception as e:
        logger.error(f"❌ خطای غیرمنتظره: {str(e)}")
        logger.exception("جزئیات خطا:")
        return False

def main():
    """تابع اصلی برنامه"""
    args = parse_args()

    # اعتبارسنجی تاریخ‌ها
    if not all(validate_date(date) for date in [args.start_date, args.end_date]):
        logger.error("❌ فرمت تاریخ نامعتبر. لطفا از فرمت YYYY-MM-DD استفاده کنید")
        sys.exit(1)

    # اعتبارسنجی آدرس دیتابیس
    if args.save_to_db and not args.db_url:
        logger.error("❌ آدرس دیتابیس مشخص نشده است. لطفا با پارامتر --db-url آدرس دیتابیس را مشخص کنید یا در فایل .env تنظیم کنید")
        sys.exit(1)

    # جمع‌آوری و ذخیره داده‌ها
    success = collect_and_store_data(
        start_date=args.start_date,
        end_date=args.end_date,
        output_dir=args.output_dir,
        request_delay=args.request_delay,
        max_retries=args.max_retries,
        max_workers=args.max_workers,
        db_url=args.db_url,
        save_to_db=args.save_to_db,
        save_to_file=args.save_to_file,
        save_raw_data=args.save_raw_data
    )

    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
