"""Create monitoring tables

Revision ID: b491bac145c9
Revises: 9af51fd40d99
Create Date: 2025-03-04 02:32:14.576872

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b491bac145c9'
down_revision: Union[str, None] = '9af51fd40d99'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('trading_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('daily_volume', sa.Float(), nullable=True),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('active_positions', sa.Integer(), nullable=True),
    sa.Column('pnl', sa.Float(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('sharpe_ratio', sa.Float(), nullable=True),
    sa.Column('market_status', sa.String(), nullable=True),
    sa.Column('volatility', sa.Float(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trading_metrics_id'), 'trading_metrics', ['id'], unique=False)
    op.add_column('alerts', sa.Column('metrics_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'alerts', 'system_metrics', ['metrics_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'alerts', type_='foreignkey')
    op.drop_column('alerts', 'metrics_id')
    op.drop_index(op.f('ix_trading_metrics_id'), table_name='trading_metrics')
    op.drop_table('trading_metrics')
    # ### end Alembic commands ###
