import sys
import asyncio
import json
import os
import random
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path
import logging
import warnings
import argparse
from datetime import datetime

import joblib
import pandas as pd
import dask.dataframe as dd
import finpy_tse as fpy
import jdatetime

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# پیکربندی اولیه logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=FutureWarning, module='finpy_tse')

# فرض کنید ماژول exceptions موجود باشد؛ در غیر این صورت می‌توانید آن‌ها را حذف کنید.
# from .exceptions import (
#     StockDataRetrievalError,
#     EmptyDataError,
#     ServiceConnectionError,
#     DateRangeError
# )

# -------------------------------
# تابع کمکی تبدیل تاریخ میلادی به شمسی
def convert_to_jalali(date_str: str) -> str:
    try:
        g_date = datetime.strptime(date_str, "%Y-%m-%d")
        j_date = jdatetime.date.fromgregorian(date=g_date.date())
        return j_date.strftime("%Y-%m-%d")
    except Exception as e:
        logger.error(f"Date conversion error for {date_str}: {str(e)}")
        return date_str  # در صورت خطا، تاریخ اصلی برگردانده شود

# -------------------------------
# ماژول اصلی جمع‌آوری داده‌ها
class MarketDataCollector:
    def __init__(self, request_delay: float = 0.5, max_retries: int = 3, max_workers: int = 8):
        self.request_delay = request_delay
        self.max_retries = max_retries
        self.max_workers = max_workers
        self.ticker_map = {}  # نگهداری نقشه normalized -> original
        self.all_stock_data = {}

    def normalize_ticker(self, ticker: str) -> str:
        """استانداردسازی نام نماد"""
        return ticker.strip()

    def get_market_stocks(self) -> dict:
        """دریافت لیست نمادهای سهام بازار به صورت دیکشنری normalized -> original"""
        logger.info("در حال دریافت لیست سهام بازار...")
        try:
            stock_list = fpy.Build_Market_StockList(
                bourse=True,
                farabourse=True,
                payeh=True,
                detailed_list=False,
                show_progress=True,
                save_excel=False,
                save_csv=False
            )
            if stock_list is None or stock_list.empty:
                logger.error("❌ لیست سهام دریافت شده خالی است")
                return {}
            logger.info("✅ لیست سهام بازار با موفقیت دریافت شد")
            ticker_map = {}
            # در اینجا از ستون 'Name' به عنوان نماد استفاده می‌کنیم، در صورت نیاز آن را تغییر دهید.
            symbol_col = 'Name' if 'Name' in stock_list.columns else stock_list.columns[0]
            for ticker in stock_list[symbol_col]:
                norm = self.normalize_ticker(ticker)
                ticker_map[norm] = ticker
            logger.info(f"📊 تعداد کل سهام: {len(ticker_map):,}")
            self.ticker_map = ticker_map
            return ticker_map
        except Exception as e:
            logger.error(f"❌ خطا در دریافت لیست سهام: {str(e)}")
            return {}

    def get_stock_history(self, norm_ticker: str, start_date: str, end_date: str, use_jalali: bool = True) -> pd.DataFrame:
        """دریافت تاریخچه قیمت سهام برای یک نماد"""
        original_ticker = self.ticker_map.get(norm_ticker, norm_ticker)
        logger.info(f"دریافت تاریخچه قیمت برای {norm_ticker} (اصلی: {original_ticker})...")
        def attempt_get(ticker_to_use: str, adjust_price: bool) -> pd.DataFrame:
            try:
                df = fpy.Get_Price_History(
                    stock=ticker_to_use,
                    start_date=start_date,
                    end_date=end_date,
                    ignore_date=False,
                    adjust_price=adjust_price,
                    show_weekday=False,
                    double_date=False
                )
                if df is not None and not df.empty:
                    # در صورت نیاز، تبدیل تاریخ به datetime انجام شود
                    if 'Date' in df.columns and use_jalali:
                        # فرض می‌کنیم تاریخ به صورت شمسی است؛ در صورت نیاز، تبدیل صورت گیرد.
                        pass
                    return df
            except Exception as ex:
                logger.warning(f"خطا در دریافت {ticker_to_use} با adjust_price={adjust_price}: {str(ex)}")
            return pd.DataFrame()
        # تلاش چندباره
        for adjust in [True, False]:
            df = attempt_get(norm_ticker, adjust)
            if not df.empty:
                return df
            df = attempt_get(original_ticker, adjust)
            if not df.empty:
                return df
        logger.error(f"داده خالی برای {norm_ticker} (اصلی: {original_ticker})")
        return pd.DataFrame()

    def extract_ri_history(self, symbol: str, start_date: str, end_date: str) -> dict:
        """استخراج اطلاعات حقیقی-حقوقی برای یک نماد، با مدیریت خطای تاریخ"""
        logger.info(f"استخراج اطلاعات حقیقی-حقوقی برای {symbol}...")
        try:
            df = fpy.Get_RI_History(
                stock=symbol,
                start_date=start_date,
                end_date=end_date,
                ignore_date=False,
                show_weekday=False,
                double_date=False,
                alt=False
            )
            # در صورت بروز خطای تاریخ، می‌توان سعی در تبدیل دستی کرد یا مقدار None برگرداند.
            return df.to_dict(orient="records") if df is not None and not df.empty else None
        except Exception as e:
            logger.error(f"خطا در استخراج ri_history برای {symbol}: {str(e)}")
            return None

    def extract_intraday_trades(self, symbol: str, start_date: str, end_date: str) -> dict:
        """استخراج ریز معاملات برای یک نماد"""
        logger.info(f"استخراج ریز معاملات برای {symbol}...")
        try:
            df = fpy.Get_IntradayTrades_History(
                stock=symbol,
                start_date=start_date,
                end_date=end_date,
                jalali_date=True,
                combined_datatime=False,
                show_progress=True
            )
            return df.to_dict(orient="records") if df is not None and not df.empty else None
        except Exception as e:
            logger.error(f"خطا در استخراج intraday_trades برای {symbol}: {str(e)}")
            return None

    def extract_intraday_orderbook(self, symbol: str, date: str) -> dict:
        """استخراج عمق بازار برای یک نماد در تاریخ مشخص"""
        logger.info(f"استخراج عمق بازار برای {symbol} در تاریخ {date}...")
        try:
            df = fpy.Get_IntradayOB_History(
                stock=symbol,
                start_date=date,
                end_date=date,
                jalali_date=True,
                combined_datatime=False,
                show_progress=True
            )
            return df.to_dict(orient="records") if df is not None and not df.empty else None
        except Exception as e:
            logger.error(f"خطا در استخراج intraday_orderbook برای {symbol}: {str(e)}")
            return None

    def extract_queue_history(self, symbol: str, start_date: str, end_date: str) -> dict:
        """استخراج وضعیت صف معاملات برای یک نماد"""
        logger.info(f"استخراج وضعیت صف معاملات برای {symbol}...")
        try:
            df = fpy.Get_Queue_History(
                stock=symbol,
                start_date=start_date,
                end_date=end_date,
                show_per_capita=True,
                show_weekday=False,
                double_date=False,
                show_progress=True
            )
            return df.to_dict(orient="records") if df is not None and not df.empty else None
        except Exception as e:
            logger.error(f"خطا در استخراج queue_history برای {symbol}: {str(e)}")
            return None

    def fetch_all_data_parallel(self, symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
        """استخراج داده‌های مختلف یک نماد به صورت موازی و ادغام در یک دیکشنری"""
        data = {}
        funcs = {
            "daily_price": lambda: self.get_stock_history(symbol, start_date, end_date).to_dict(orient="records"),
            "ri_history": lambda: self.extract_ri_history(symbol, start_date, end_date),
            "intraday_trades": lambda: self.extract_intraday_trades(symbol, start_date, end_date),
            "intraday_orderbook": lambda: self.extract_intraday_orderbook(symbol, orderbook_date),
            "queue_history": lambda: self.extract_queue_history(symbol, start_date, end_date)
        }
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_map = {executor.submit(func): key for key, func in funcs.items()}
            for future in as_completed(future_map):
                key = future_map[future]
                try:
                    result = future.result()
                    data[key] = result
                except Exception as ex:
                    logger.error(f"خطا در استخراج {key} برای {symbol}: {str(ex)}")
                    data[key] = None
        return data

    def process_symbol(self, symbol: str, start_date: str, end_date: str, orderbook_date: str):
        """پردازش کامل یک نماد: استخراج داده‌ها و ذخیره به صورت JSON ادغام‌شده"""
        logger.info(f"\n===== شروع پردازش {symbol} =====")
        merged_data = self.fetch_all_data_parallel(symbol, start_date, end_date, orderbook_date)
        self.save_raw_data(symbol, merged_data)
        logger.info(f"===== پایان پردازش {symbol} =====\n")

    def save_raw_data(self, symbol: str, raw_data: dict):
        """ذخیره داده‌های ادغام‌شده یک نماد به صورت JSON در فایل (همچنین می‌توان به پایگاه داده ذخیره کرد)"""
        try:
            output_dir = Path("output")
            output_dir.mkdir(parents=True, exist_ok=True)
            file_path = output_dir / f"{symbol}_raw_data.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(raw_data, f, ensure_ascii=False, indent=4)
            logger.info(f"داده‌های {symbol} در '{file_path}' ذخیره شدند.")
        except Exception as e:
            logger.error(f"خطا در ذخیره داده‌های {symbol}: {str(e)}")

    def collect_all_data(self, start_date: str, end_date: str, orderbook_date: str, batch_size: int = 20) -> dict:
        """جمع‌آوری داده برای تمامی نمادها به صورت دسته‌ای"""
        ticker_map = self.get_market_stocks()
        if not ticker_map:
            logger.error("هیچ نمادی یافت نشد")
            return {}

        tickers = list(ticker_map.keys())
        total_batches = (len(tickers) + batch_size - 1) // batch_size
        for batch_idx in range(total_batches):
            batch_tickers = tickers[batch_idx*batch_size:(batch_idx+1)*batch_size]
            logger.info(f"\n📦 پردازش دسته {batch_idx+1}/{total_batches}")
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(self.process_symbol, ticker, start_date, end_date, orderbook_date): ticker for ticker in batch_tickers}
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as ex:
                        logger.error(f"خطا در پردازش {futures[future]}: {str(ex)}")
            # ذخیره نتایج میانی
            self.save_intermediate_results(batch_idx)
            time.sleep(2)
        return self.all_stock_data

    def save_intermediate_results(self, batch_id: int):
        """ذخیره نتایج میانی به کمک joblib"""
        try:
            cache_dir = Path("cache/intermediate")
            cache_dir.mkdir(parents=True, exist_ok=True)
            joblib.dump(self.all_stock_data, cache_dir / f"batch_{batch_id}.joblib")
            logger.info(f"نتایج میانی دسته {batch_id} ذخیره شدند.")
        except Exception as e:
            logger.error(f"خطا در ذخیره نتایج میانی دسته {batch_id}: {str(e)}")

# -------------------------------
def main():
    parser = argparse.ArgumentParser(description="جمع‌آوری داده‌های بازار سهام تهران")
    parser.add_argument("start_date", help="تاریخ شروع (مثلاً 1403-12-01)")
    parser.add_argument("end_date", help="تاریخ پایان (مثلاً 1403-12-10)")
    parser.add_argument("--orderbook_date", default="1400-08-01", help="تاریخ دریافت عمق بازار")
    args = parser.parse_args()

    start_date = args.start_date
    end_date = args.end_date
    orderbook_date = args.orderbook_date

    logger.info(f"شروع: {start_date}, پایان: {end_date}, عمق بازار در: {orderbook_date}")

    collector = MarketDataCollector(request_delay=0.5, max_retries=3, max_workers=4)
    ticker_map = collector.get_market_stocks()
    if not ticker_map:
        logger.error("لیست نمادها دریافت نشد. اجرای برنامه متوقف می‌شود.")
        return

    # پردازش نمادها به صورت دسته‌ای (مثلاً batch_size=20)
    collector.collect_all_data(start_date, end_date, orderbook_date, batch_size=20)

if __name__ == '__main__':
    main()
