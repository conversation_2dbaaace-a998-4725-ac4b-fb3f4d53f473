import logging
import sys
from datetime import datetime, timedelta
import argparse
from pathlib import Path
from typing import Optional
import pandas as pd  # اضافه کردن import pandas

from ..infrastructure.external.market.market_data import MarketDataCollector
from ..infrastructure.external.exceptions import MarketDataError
from ..infrastructure.database.market_storage import MarketStorage

# تنظیم لاگر
log_path = Path('logs')
log_path.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_path / 'market_data_collection.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """پردازش آرگومان‌های ورودی برنامه"""
    parser = argparse.ArgumentParser(description="جمع‌آوری داده‌های بازار سهام تهران")
    parser.add_argument(
        "--start-date",
        help="تاریخ شروع جمع‌آوری داده‌ها (YYYY-MM-DD)",
        type=str,
        default=(datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--end-date",
        help="تاریخ پایان جمع‌آوری داده‌ها (YYYY-MM-DD)",
        type=str,
        default=datetime.now().strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--output-dir",
        help="مسیر ذخیره داده‌های خروجی",
        type=str,
        default="data/market"
    )
    parser.add_argument(
        "--request-delay",
        help="تاخیر بین درخواست‌ها (ثانیه)",
        type=float,
        default=0.5
    )
    parser.add_argument(
        "--max-retries",
        help="حداکثر تعداد تلاش‌های مجدد",
        type=int,
        default=3
    )
    parser.add_argument(
        "--max-workers",
        help="حداکثر تعداد پردازش‌های همزمان",
        type=int,
        default=4
    )
    parser.add_argument(
        "--db-url",
        help="آدرس اتصال به دیتابیس",
        type=str,
        default=None
    )
    parser.add_argument(
        "--batch-size",
        help="تعداد نمادها در هر دسته",
        type=int,
        default=2000  # افزایش اندازه batch
    )
    
    return parser.parse_args()

def validate_date(date_str: str) -> bool:
    """اعتبارسنجی فرمت تاریخ"""
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def ensure_directory(path: str) -> Path:
    """اطمینان از وجود دایرکتوری و ایجاد آن در صورت نیاز"""
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path

def collect_and_save_data(
    start_date: str,
    end_date: str,
    output_dir: str,
    request_delay: float,
    max_retries: int,
    max_workers: int,
    db_url: Optional[str] = None
) -> bool:
    """جمع‌آوری و ذخیره داده‌های بازار"""
    try:
        # ایجاد مسیر خروجی
        output_path = ensure_directory(output_dir)
        
        # ایجاد نمونه از کلاس جمع‌کننده داده
        collector = MarketDataCollector(
            request_delay=1.0,  # Increased delay
            max_retries=3,
            max_workers=4  # Reduced workers
        )
        
        logger.info(f"🔄 شروع جمع‌آوری داده‌های بازار از {start_date} تا {end_date}")
        
        # جمع‌آوری داده‌ها
        raw_data = collector.get_raw_market_data(start_date, end_date)
        
        if not raw_data:
            logger.error("❌ هیچ داده‌ای دریافت نشد")
            return False
            
        # نمایش خلاصه داده‌های جمع‌آوری شده
        logger.info("\n📊 خلاصه داده‌های دریافت شده:")
        for data_type, data in raw_data.items():
            if isinstance(data, dict):
                logger.info(f"  - {data_type}: {len(data)} نماد")
            elif isinstance(data, pd.DataFrame):
                logger.info(f"  - {data_type}: {len(data)} رکورد")
                # ذخیره DataFrame‌ها به صورت CSV
                output_file = output_path / f"{data_type}.csv"
                data.to_csv(output_file, index=False, encoding='utf-8-sig')
            else:
                logger.info(f"  - {data_type}: {type(data)}")

        # ذخیره در فایل
        collector.save_data(raw_data, str(output_path))
        logger.info(f"✅ داده‌ها در مسیر {output_path} ذخیره شدند")

        # ذخیره در دیتابیس (اگر آدرس دیتابیس ارائه شده باشد)
        if db_url:
            storage = MarketStorage(db_url)
            storage.save_market_data(raw_data.get('stock_prices', {}))
            logger.info("✅ داده‌ها در دیتابیس ذخیره شدند")

        return True

    except MarketDataError as e:
        logger.error(f"❌ خطا در جمع‌آوری داده‌ها: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ خطای غیرمنتظره: {str(e)}")
        logger.exception("جزئیات خطا:")
        return False

def main():
    """تابع اصلی برنامه"""
    args = parse_args()

    # اعتبارسنجی تاریخ‌ها
    if not all(validate_date(date) for date in [args.start_date, args.end_date]):
        logger.error("❌ فرمت تاریخ نامعتبر. لطفا از فرمت YYYY-MM-DD استفاده کنید")
        sys.exit(1)

    # جمع‌آوری و ذخیره داده‌ها
    success = collect_and_save_data(
        start_date=args.start_date,
        end_date=args.end_date,
        output_dir=args.output_dir,
        request_delay=args.request_delay,
        max_retries=args.max_retries,
        max_workers=8,  # افزایش از 4 به 8
        db_url=args.db_url
    )

    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()







