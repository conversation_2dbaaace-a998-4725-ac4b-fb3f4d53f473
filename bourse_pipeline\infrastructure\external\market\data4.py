import sys
import asyncio
import time
import random
import logging
import argparse
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

import pandas as pd
import joblib
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import finpy_tse as fpy

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# پیکربندی logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# تنظیمات پایگاه داده PostgreSQL
DB_USER = "postgres"
DB_PASSWORD = "Honey33454"
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "raw_dat"
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def get_db_engine():
    """ایجاد engine پایگاه داده با استفاده از SQLAlchemy"""
    try:
        engine = create_engine(DATABASE_URL)
        logger.info("اتصال به پایگاه داده برقرار شد.")
        return engine
    except SQLAlchemyError as e:
        logger.error(f"خطا در اتصال به پایگاه داده: {e}")
        return None

def get_market_stocks() -> dict:
    """
    دریافت لیست نمادهای بازار به صورت دیکشنری: normalized -> original.
    در این مثال از ستون 'Name' استفاده می‌شود.
    """
    logger.info("در حال دریافت لیست سهام بازار...")
    try:
        stock_list = fpy.Build_Market_StockList(
            bourse=True,
            farabourse=True,
            payeh=True,
            detailed_list=False,
            show_progress=True,
            save_excel=False,
            save_csv=False
        )
        if stock_list is None or stock_list.empty:
            logger.error("❌ لیست سهام دریافت شده خالی است.")
            return {}
        logger.info("✅ لیست سهام بازار با موفقیت دریافت شد.")
        ticker_map = {}
        symbol_col = 'Name' if 'Name' in stock_list.columns else stock_list.columns[0]
        for ticker in stock_list[symbol_col]:
            norm = ticker.strip()
            ticker_map[norm] = ticker  # نگهداری نام اصلی
        logger.info(f"📊 تعداد نمادها: {len(ticker_map):,}")
        return ticker_map
    except Exception as e:
        logger.error(f"❌ خطا در دریافت لیست سهام: {e}")
        return {}

def get_stock_history(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    استخراج تاریخچه قیمت روزانه یک نماد.
    - ستون‌های اصلی شامل: Date, Open, High, Low, Close, Volume.
    - ستون Date به datetime تبدیل می‌شود.
    - ستون EPS در صورت موجود بودن افزوده می‌شود؛ در غیر این صورت مقدار NaN قرار می‌گیرد.
    """
    logger.info(f"استخراج تاریخچه قیمت برای {symbol}...")
    try:
        df = fpy.Get_Price_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            adjust_price=True,
            show_weekday=False,
            double_date=False
        )
        if df is None or df.empty:
            logger.warning(f"داده‌ای برای {symbol} دریافت نشد.")
            return pd.DataFrame()
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        available_cols = [col for col in required_columns if col in df.columns]
        df = df[available_cols].copy()
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        df['symbol'] = symbol
        # ستون EPS در صورت موجودیت؛ در غیر این صورت NaN
        df['EPS'] = df.get('EPS', pd.NA)
        return df
    except Exception as e:
        logger.error(f"خطا در استخراج تاریخچه قیمت برای {symbol}: {e}")
        return pd.DataFrame()

def extract_ri_history(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج اطلاعات حقیقی-حقوقی برای یک نماد"""
    logger.info(f"استخراج اطلاعات حقیقی-حقوقی برای {symbol}...")
    try:
        df = fpy.Get_RI_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            show_weekday=False,
            double_date=False,
            alt=False
        )
        # تبدیل تاریخ‌ها در صورت نیاز
        if df is not None and 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        return df.to_dict(orient="records") if df is not None and not df.empty else None
    except Exception as e:
        logger.error(f"خطا در استخراج ri_history برای {symbol}: {e}")
        return None

def extract_intraday_trades(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج ریز معاملات برای یک نماد"""
    logger.info(f"استخراج ریز معاملات برای {symbol}...")
    try:
        df = fpy.Get_IntradayTrades_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else None
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_trades برای {symbol}: {e}")
        return None

def extract_intraday_orderbook(symbol: str, date: str) -> dict:
    """استخراج عمق بازار برای یک نماد در تاریخ مشخص"""
    logger.info(f"استخراج عمق بازار برای {symbol} در تاریخ {date}...")
    try:
        df = fpy.Get_IntradayOB_History(
            stock=symbol,
            start_date=date,
            end_date=date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else None
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_orderbook برای {symbol}: {e}")
        return None

def extract_queue_history(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج وضعیت صف معاملات برای یک نماد"""
    logger.info(f"استخراج وضعیت صف معاملات برای {symbol}...")
    try:
        df = fpy.Get_Queue_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            show_per_capita=True,
            show_weekday=False,
            double_date=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else None
    except Exception as e:
        logger.error(f"خطا در استخراج queue_history برای {symbol}: {e}")
        return None

def fetch_all_data_parallel(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
    """
    استخراج داده‌های مختلف (تاریخچه قیمت، ri_history، ریز معاملات، عمق بازار و وضعیت صف معاملات)
    برای یک نماد به صورت موازی و ادغام آن‌ها در یک دیکشنری.
    """
    data = {}
    funcs = {
        "daily_price": lambda: get_stock_history(symbol, start_date, end_date).to_dict(orient="records"),
        "ri_history": lambda: extract_ri_history(symbol, start_date, end_date),
        "intraday_trades": lambda: extract_intraday_trades(symbol, start_date, end_date),
        "intraday_orderbook": lambda: extract_intraday_orderbook(symbol, orderbook_date),
        "queue_history": lambda: extract_queue_history(symbol, start_date, end_date)
    }
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_map = {executor.submit(func): key for key, func in funcs.items()}
        for future in as_completed(future_map):
            key = future_map[future]
            try:
                result = future.result()
                data[key] = result
            except Exception as ex:
                logger.error(f"خطا در استخراج {key} برای {symbol}: {ex}")
                data[key] = None
    return data

def process_symbol(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> pd.DataFrame:
    """
    پردازش کامل یک نماد:
      - استخراج داده‌های روزانه (قیمت) جهت ادغام در DataFrame یکپارچه
      - استخراج سایر داده‌ها و ذخیره به صورت فایل JSON به عنوان داده خام
    """
    logger.info(f"\n===== شروع پردازش {symbol} =====")
    # استخراج داده‌های روزانه جهت ادغام
    price_df = get_stock_history(symbol, start_date, end_date)
    # استخراج سایر داده‌ها به صورت موازی
    extra_data = fetch_all_data_parallel(symbol, start_date, end_date, orderbook_date)
    # ذخیره داده‌های اضافی (همچنین می‌توانید در پایگاه داده ذخیره کنید)
    save_raw_data(symbol, extra_data)
    logger.info(f"===== پایان پردازش {symbol} =====\n")
    return price_df

def save_raw_data(symbol: str, raw_data: dict):
    """ذخیره داده‌های ادغام‌شده یک نماد به صورت JSON در پوشه output"""
    try:
        output_dir = Path("output")
        output_dir.mkdir(parents=True, exist_ok=True)
        file_path = output_dir / f"{symbol}_raw_data.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(raw_data, f, ensure_ascii=False, indent=4)
        logger.info(f"داده‌های {symbol} در '{file_path}' ذخیره شدند.")
    except Exception as e:
        logger.error(f"خطا در ذخیره داده‌های {symbol}: {e}")

def process_all_stocks(start_date: str, end_date: str, orderbook_date: str, max_workers: int = 8) -> pd.DataFrame:
    """
    پردازش تمامی نمادها:
      - استخراج داده‌های روزانه برای هر نماد (برای ادغام در DataFrame یکپارچه جهت ذخیره در پایگاه داده)
      - انجام پردازش به صورت دسته‌ای (batch) و موازی
    """
    ticker_map = get_market_stocks()
    if not ticker_map:
        logger.error("لیست نمادها دریافت نشد.")
        return pd.DataFrame()

    consolidated_price_data = []
    tickers = list(ticker_map.keys())
    total_batches = (len(tickers) + 19) // 20  # batch_size=20

    for batch_idx in range(total_batches):
        batch_tickers = tickers[batch_idx*20:(batch_idx+1)*20]
        logger.info(f"\n📦 پردازش دسته {batch_idx+1}/{total_batches}")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_symbol, ticker, start_date, end_date, orderbook_date): ticker for ticker in batch_tickers}
            for future in as_completed(futures):
                symbol = futures[future]
                try:
                    price_df = future.result()
                    if not price_df.empty:
                        consolidated_price_data.append(price_df)
                except Exception as ex:
                    logger.error(f"خطا در پردازش {symbol}: {ex}")
        # ذخیره نتایج میانی با joblib (اختیاری)
        save_intermediate_results(batch_idx)
        time.sleep(2)

    if consolidated_price_data:
        combined_df = pd.concat(consolidated_price_data, ignore_index=True)
        logger.info(f"✅ داده‌های استخراج‌شده قیمت برای تمام نمادها ادغام شدند. تعداد رکوردها: {len(combined_df)}")
        return combined_df
    else:
        logger.error("هیچ داده‌ای استخراج نشد.")
        return pd.DataFrame()

def save_intermediate_results(batch_id: int):
    """ذخیره نتایج میانی به کمک joblib"""
    try:
        cache_dir = Path("cache/intermediate")
        cache_dir.mkdir(parents=True, exist_ok=True)
        joblib.dump({}, cache_dir / f"batch_{batch_id}.joblib")
        logger.info(f"نتایج میانی دسته {batch_id} ذخیره شدند.")
    except Exception as e:
        logger.error(f"خطا در ذخیره نتایج میانی دسته {batch_id}: {e}")

def save_to_postgresql(df: pd.DataFrame, table_name: str = "stock_data"):
    """
    ذخیره DataFrame نهایی در یک جدول یکپارچه PostgreSQL.
    اگر جدول وجود داشته باشد، داده‌ها افزوده می‌شوند.
    """
    engine = get_db_engine()
    if engine is None:
        logger.error("اتصال به پایگاه داده برقرار نشد. ذخیره داده لغو می‌شود.")
        return
    try:
        df.to_sql(table_name, engine, if_exists="append", index=False, method="multi")
        logger.info(f"✅ داده‌ها در جدول '{table_name}' ذخیره شدند.")
    except Exception as e:
        logger.error(f"خطا در ذخیره داده‌ها در جدول '{table_name}': {e}")

def main():
    parser = argparse.ArgumentParser(
        description="دریافت و ادغام داده‌های روزانه سهام (همراه با داده‌های اضافی) و ذخیره در PostgreSQL"
    )
    parser.add_argument("start_date", help="تاریخ شروع (مثلاً 1403-12-01)")
    parser.add_argument("end_date", help="تاریخ پایان (مثلاً 1403-12-10)")
    parser.add_argument("--orderbook_date", default="1400-08-01", help="تاریخ دریافت عمق بازار")
    parser.add_argument("--max_workers", type=int, default=8, help="حداکثر تعداد نخ‌های موازی")
    args = parser.parse_args()

    start_date = args.start_date
    end_date = args.end_date
    orderbook_date = args.orderbook_date

    logger.info(f"شروع استخراج داده‌ها از {start_date} تا {end_date}، عمق بازار در: {orderbook_date}")
    consolidated_df = process_all_stocks(start_date, end_date, orderbook_date, max_workers=args.max_workers)
    if consolidated_df.empty:
        logger.error("هیچ داده‌ای استخراج نشد. خروج از برنامه.")
        return

    # ذخیره DataFrame یکپارچه قیمت در پایگاه داده PostgreSQL
    save_to_postgresql(consolidated_df, table_name="stock_data")

if __name__ == '__main__':
    main()
