import pandas as pd
from sqlalchemy import create_engine
import logging

logger = logging.getLogger(__name__)

class MarketStorage:
    def __init__(self, db_url: str):
        self.engine = create_engine(db_url)
        
    def save_market_data(self, data_dict: dict[str, pd.DataFrame]):
        """ذخیره دیکشنری داده‌های بازار در دیتابیس"""
        try:
            for symbol, df in data_dict.items():
                df.to_sql('market_data', self.engine, if_exists='append', index=False)
                logger.info(f"✅ داده‌های {symbol} با {len(df)} رکورد ذخیره شد")
        except Exception as e:
            logger.error(f"خطا در ذخیره داده‌ها: {str(e)}")
            raise