import finpy_tse as fpy
import pandas as pd
import time
import logging
import json
import argparse
import joblib
import warnings
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import jdatetime
from pathlib import Path
import os
import dask.dataframe as dd
from datetime import datetime
from typing import Dict, List, Optional
from unicodedata import normalize
import random

# واردسازی استثناها (توجه به صحت مسیر واردسازی)
from .exceptions import (
    StockDataRetrievalError,
    EmptyDataError,
    ServiceConnectionError,
    DateRangeError
)

# پیکربندی اولیه logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

warnings.filterwarnings('ignore', category=FutureWarning, module='finpy_tse')


class MarketDataCollector:
    def __init__(self, config: Dict):
        self.request_delay = config['data_ingestion']['request_delay']
        self.batch_size = config['data_ingestion']['batch_size']
        self.max_workers = config['data_ingestion']['max_workers']
        self.retry_attempts = config['data_ingestion']['retry_attempts']
        
        # تنظیمات شبیه‌سازی
        self.human_sim = config['data_ingestion']['human_simulation']
        
    def _apply_human_delay(self):
        """اعمال تاخیر شبیه‌سازی شده انسانی"""
        if self.human_sim['enabled']:
            delay = random.uniform(
                self.human_sim['min_delay'],
                self.human_sim['max_delay']
            )
            time.sleep(delay)
            
    def _check_random_pause(self, request_count: int):
        """بررسی نیاز به توقف تصادفی"""
        if (self.human_sim['enabled'] and 
            self.human_sim['random_pause'] and 
            request_count % self.human_sim['pause_interval'] == 0):
            
            pause_time = random.uniform(
                self.human_sim['pause_duration'][0],
                self.human_sim['pause_duration'][1]
            )
            logger.info(f"توقف کوتاه به مدت {pause_time:.1f} ثانیه...")
            time.sleep(pause_time)

    async def collect_market_data(self, symbols: list) -> Dict:
        """جمع‌آوری داده‌های بازار با رعایت محدودیت‌ها"""
        results = {}
        request_count = 0
        
        for batch in self._create_batches(symbols, self.batch_size):
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                for symbol in batch:
                    futures.append(
                        executor.submit(self._fetch_symbol_data, symbol)
                    )
                    request_count += 1
                    self._apply_human_delay()
                    
                for future in as_completed(futures):
                    try:
                        data = future.result()
                        if data:
                            results.update(data)
                    except Exception as e:
                        logger.error(f"خطا در دریافت داده: {e}")
                        
            self._check_random_pause(request_count)
            
        return results

    def normalize_ticker(self, ticker: str) -> str:
        """
        استانداردسازی نام نماد (حذف فضاهای اضافی و نرمال‌سازی کاراکترها)
        """
        return normalize('NFKC', ticker.strip())

    def _convert_to_jalali(self, date_str: str) -> Optional[str]:
        """
        (در صورت نیاز) تبدیل تاریخ میلادی به شمسی؛
        طبق مستندات fpy، توابع اصلی انتظار دریافت تاریخ شمسی را دارند.
        """
        try:
            if pd.isna(date_str):
                logger.error(f"Invalid date value: {date_str}")
                return None
            if isinstance(date_str, str):
                g_date = datetime.strptime(date_str, "%Y-%m-%d")
            else:
                g_date = date_str
            j_date = jdatetime.date.fromgregorian(date=g_date.date())
            return j_date.strftime("%Y-%m-%d")
        except Exception as e:
            logger.error(f"Date conversion error for {date_str}: {str(e)}")
            return None

    def _process_partition(self, df: pd.DataFrame) -> pd.DataFrame:
        """پردازش هر پارتیشن DataFrame (قابلیت گسترش بر اساس نیاز)"""
        return df

    def process_data(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """پردازش موازی داده‌ها با استفاده از Dask DataFrame."""
        try:
            concatenated_df = pd.concat(data.values())
        except Exception as e:
            logger.error(f"Error concatenating data: {str(e)}")
            raise StockDataRetrievalError("Error concatenating data.") from e

        ddf = dd.from_pandas(concatenated_df, npartitions=self.max_workers)
        result = ddf.map_partitions(self._process_partition)
        return result.compute()

    def get_raw_market_data(self, start_date: str, end_date: str, use_jalali: bool = True) -> Dict[str, Dict]:
        """
        دریافت تمام داده‌های خام مورد نیاز برای تحلیل‌های مختلف.
        تاریخ‌ها طبق مستندات باید به صورت شمسی ارسال شوند.
        """
        try:
            logger.info("شروع جمع‌آوری داده‌های خام بازار...")

            raw_data = {
                'stock_prices': self.collect_all_price_data(start_date, end_date, use_jalali),
                'market_indices': self.get_market_indices(start_date, end_date),
                'trading_volume': self.get_trading_volumes(start_date, end_date),
                'market_stats': self.get_market_statistics(start_date, end_date),
                'stock_info': self.get_stocks_information()
            }

            logger.info("✅ جمع‌آوری داده‌های خام با موفقیت انجام شد")
            return raw_data

        except Exception as e:
            logger.error(f"❌ خطا در جمع‌آوری داده‌های خام: {str(e)}")
            raise

    def get_market_stocks(self) -> Dict[str, str]:
        """
        دریافت لیست نمادهای سهام بازار.
        خروجی یک دیکشنری است که کلید آن نام نرمال‌شده و مقدار آن نام اصلی است.
        """
        logger.info("در حال دریافت لیست سهام بازار...")
        try:
            stock_list = fpy.Build_Market_StockList(
                bourse=True,
                farabourse=True,
                payeh=True,
                detailed_list=False,
                show_progress=False,
                save_excel=False,
                save_csv=False
            )
            if stock_list is None or stock_list.empty:
                logger.error("❌ لیست سهام دریافت شده خالی است")
                return {}

            logger.info("✅ لیست سهام بازار با موفقیت دریافت شد")
            logger.debug(f"نمونه داده لیست سهام:\n{stock_list.head()}")

            ticker_map = {}
            for ticker in stock_list.index.to_list():
                norm = self.normalize_ticker(ticker)
                ticker_map[norm] = ticker  # نگهداری نام اصلی
            logger.info(f"📊 تعداد کل سهام: {len(ticker_map):,}")
            self.ticker_map = ticker_map
            return ticker_map

        except Exception as e:
            logger.error(f"❌ خطا در دریافت لیست سهام: {str(e)}")
            return {}

    def get_stock_history(self, norm_ticker: str, start_date: str, end_date: str, use_jalali: bool = True) -> Optional[pd.DataFrame]:
        """
        دریافت تاریخچه قیمت سهام.
        ابتدا از نام نرمال‌شده استفاده می‌شود؛ در صورت عدم دریافت داده، تلاش می‌شود با نام اصلی نیز فراخوانی شود.
        """
        original_ticker = self.ticker_map.get(norm_ticker, norm_ticker)
        logger.info(f"در حال دریافت تاریخچه قیمت برای نماد (نرمال): {norm_ticker}، اصلی: {original_ticker}...")
        
        def attempt_get(ticker_to_use: str, adjust_price: bool) -> Optional[pd.DataFrame]:
            try:
                df = fpy.Get_Price_History(
                    stock=ticker_to_use,
                    start_date=start_date,
                    end_date=end_date,
                    ignore_date=False,
                    adjust_price=adjust_price,
                    show_weekday=False,
                    double_date=False
                )
                if df is not None and not df.empty:
                    if not use_jalali:
                        df['Date'] = pd.to_datetime(df['Date'])
                    return df
            except Exception as ex:
                logger.warning(f"خطا در دریافت تاریخچه {ticker_to_use} با adjust_price={adjust_price}: {str(ex)}")
            return None

        # تلاش اول با نام نرمال‌شده
        for adjust in [True, False]:
            df = attempt_get(norm_ticker, adjust)
            if df is not None and not df.empty:
                return df
            # در صورت عدم موفقیت، تلاش با نام اصلی
            df = attempt_get(original_ticker, adjust)
            if df is not None and not df.empty:
                return df

        logger.error(f"داده خالی برای نماد {norm_ticker} (نام اصلی: {original_ticker}) پس از تلاش‌های متعدد.")
        # به جای raise کردن خطا، یک DataFrame خالی برمی‌گردانیم
        return pd.DataFrame()

    def collect_all_price_data(self, start_date: str, end_date: str, use_jalali: bool = True) -> Dict[str, pd.DataFrame]:
        """
        دریافت دسته‌جمعی سابقه قیمت سهام با استفاده از پردازش موازی.
        """
        ticker_map = self.get_market_stocks()  # دیکشنری: normalized -> original
        if not ticker_map:
            logger.error("هیچ نمادی دریافت نشد")
            return {}

        price_data = {}
        failed_tickers = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_ticker = {
                executor.submit(self.get_stock_history, norm, start_date, end_date, use_jalali): norm
                for norm in ticker_map.keys()
            }

            for future in as_completed(future_to_ticker):
                norm_ticker = future_to_ticker[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Close_Adj']
                        available_columns = [col for col in required_columns if col in df.columns]
                        if not available_columns:
                            logger.error(f"ستون‌های مورد نیاز برای {norm_ticker} یافت نشد. ستون‌های موجود: {df.columns.tolist()}")
                            failed_tickers.append(norm_ticker)
                        else:
                            price_data[norm_ticker] = df[available_columns].copy()
                            logger.debug(f"داده {norm_ticker} با موفقیت دریافت شد.")
                    else:
                        logger.error(f"DataFrame خالی یا None برای {norm_ticker}")
                        failed_tickers.append(norm_ticker)
                except Exception as e:
                    logger.error(f"خطا در دریافت داده برای {norm_ticker}: {str(e)}", exc_info=True)
                    failed_tickers.append(norm_ticker)

        if failed_tickers:
            logger.warning(f"داده دریافت نشد برای {len(failed_tickers)} نماد: {', '.join(failed_tickers)}")

        return price_data

    def get_market_indices(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """دریافت شاخص‌های اصلی بازار."""
        logger.info("در حال دریافت شاخص‌های اصلی بازار...")
        try:
            indices = fpy.Get_MarketWatch(save_excel=False)
            if isinstance(indices, tuple):
                indices = indices[0]
            logger.info("✅ دریافت شاخص‌های اصلی بازار با موفقیت انجام شد.")
            return indices
        except Exception as e:
            logger.error(f"❌ خطا در دریافت شاخص‌های بازار: {str(e)}")
            return None

    def get_trading_volumes(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """دریافت حجم و ارزش معاملات کل بازار."""
        logger.info("در حال دریافت حجم و ارزش معاملات کل بازار...")
        try:
            market_stats = fpy.Get_MarketWatch(save_excel=False)
            if isinstance(market_stats, tuple):
                market_stats = market_stats[0]
            columns = ['Volume', 'Value'] if all(col in market_stats.columns for col in ['Volume', 'Value']) else market_stats.columns[:2]
            logger.info("✅ دریافت حجم و ارزش معاملات کل بازار با موفقیت انجام شد.")
            return market_stats[columns]
        except Exception as e:
            logger.error(f"❌ خطا در دریافت حجم معاملات: {str(e)}")
            return None

    def get_market_statistics(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """دریافت آمار کلی بازار."""
        logger.info("در حال دریافت آمار کلی بازار...")
        try:
            stats = fpy.Get_MarketWatch(save_excel=False)
            if isinstance(stats, tuple):
                stats = stats[0]
            logger.info("✅ دریافت آمار کلی بازار با موفقیت انجام شد.")
            return stats
        except Exception as e:
            logger.error(f"❌ خطا در دریافت آمار بازار: {str(e)}")
            return None

    def get_stocks_information(self) -> Optional[pd.DataFrame]:
        """دریافت لیست جامع و مشخصات سهام بازار."""
        logger.info("در حال دریافت اطلاعات پایه‌ای سهام...")
        try:
            stocks_info = fpy.Build_Market_StockList(
                bourse=True,
                farabourse=True,
                payeh=True,
                detailed_list=True,
                show_progress=True,
                save_excel=False,
                save_csv=False
            )
            if stocks_info is None or stocks_info.empty:
                logger.error("داده‌های اطلاعات سهام خالی هستند.")
                return None
            logger.info("✅ دریافت اطلاعات پایه‌ای سهام با موفقیت انجام شد.")
            return stocks_info
        except Exception as e:
            logger.error(f"❌ خطا در دریافت اطلاعات سهام: {str(e)}")
            return None

    def collect_all_data(self, start_date: str, end_date: str, batch_size: int = 20) -> Dict[str, pd.DataFrame]:
        """جمع‌آوری دسته‌جمعی داده‌های سهام با پردازش دسته‌ای."""
        ticker_map = self.get_market_stocks()
        if not ticker_map:
            logger.error("هیچ نمادی یافت نشد")
            return {}

        tickers = list(ticker_map.keys())
        total_batches = (len(tickers) + batch_size - 1) // batch_size
        successful_count = 0
        failed_tickers = []
        empty_data_tickers = []
        self.all_stock_data = {}

        for batch_idx in range(total_batches):
            batch_start = batch_idx * batch_size
            batch_end = min(batch_start + batch_size, len(tickers))
            batch_tickers = tickers[batch_start:batch_end]

            logger.info(f"\n📦 پردازش دسته {batch_idx + 1}/{total_batches}")

            for ticker in tqdm(batch_tickers, desc=f"دسته {batch_idx + 1}/{total_batches}"):
                df = self.get_stock_history(ticker, start_date, end_date)
                time.sleep(self.request_delay)
                
                if df is not None and not df.empty:
                    self.all_stock_data[ticker] = df
                    successful_count += 1
                else:
                    empty_data_tickers.append(ticker)
                    logger.warning(f"داده خالی برای نماد {ticker}")
                    
            self.save_intermediate_results(self.all_stock_data, batch_idx)
            if batch_idx < total_batches - 1:
                time.sleep(2)

        self._log_final_report(tickers, successful_count, failed_tickers, empty_data_tickers)
        return self.all_stock_data

    def _log_final_report(self, tickers, successful_count, failed_tickers, empty_data_tickers):
        """گزارش‌گیری نهایی از وضعیت جمع‌آوری داده‌ها"""
        logger.info("\n📊 خلاصه جمع‌آوری داده‌ها:")
        logger.info(f"  - تعداد کل نمادها: {len(tickers):,}")
        logger.info(f"  - موفق: {successful_count:,}")
        logger.info(f"  - شکست: {len(failed_tickers):,}")
        logger.info(f"  - داده خالی: {len(empty_data_tickers):,}")

        if failed_tickers:
            logger.warning(f"⚠️ نمادهای ناموفق: {', '.join(failed_tickers)}")

    def save_data(self, raw_data: Dict[str, Dict], output_dir: str = 'output'):
        """ذخیره داده‌های خام در فایل‌های JSON."""
        os.makedirs(output_dir, exist_ok=True)
        for data_type, data in raw_data.items():
            file_path = os.path.join(output_dir, f'{data_type}.json')
            try:
                if isinstance(data, dict):
                    serializable_data = {
                        key: value.to_dict(orient='records') if isinstance(value, pd.DataFrame) else value
                        for key, value in data.items()
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(serializable_data, f, ensure_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های '{data_type}' در '{file_path}' ذخیره شدند.")
                elif isinstance(data, pd.DataFrame):
                    data.to_json(file_path, orient='records', force_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های '{data_type}' در '{file_path}' ذخیره شدند.")
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های '{data_type}' در '{file_path}' ذخیره شدند.")
            except Exception as e:
                logger.error(f"❌ خطا در ذخیره داده‌های '{data_type}' در '{file_path}': {str(e)}")

    def save_intermediate_results(self, data: Dict[str, pd.DataFrame], batch_id: int):
        """ذخیره نتایج میانی با استفاده از joblib."""
        try:
            cache_dir = Path("cache/intermediate")
            cache_dir.mkdir(parents=True, exist_ok=True)
            joblib.dump(data, cache_dir / f"batch_{batch_id}.joblib")
            logger.info(f"✅ نتایج میانی دسته {batch_id} ذخیره شدند.")
        except Exception as e:
            logger.error(f"❌ خطا در ذخیره نتایج میانی دسته {batch_id}: {str(e)}")


def main():
    """
    تابع اصلی برنامه برای جمع‌آوری داده‌های بازار سهام تهران.
    توجه کنید که تاریخ‌ها طبق مستندات fpy به صورت شمسی ارسال می‌شوند.
    """
    parser = argparse.ArgumentParser(description="جمع‌آوری داده‌های بازار سهام تهران")
    parser.add_argument("start_date", help="تاریخ شروع جمع‌آوری داده‌ها (مثلاً 1403-12-01)")
    parser.add_argument("end_date", help="تاریخ پایان جمع‌آوری داده‌ها (مثلاً 1403-12-10)")
    parser.add_argument("--output_dir", default="output", help="مسیر ذخیره داده‌های خروجی")
    args = parser.parse_args()

    try:
        start_date = args.start_date
        end_date = args.end_date
        output_dir = args.output_dir

        logger.info(f"تاریخ شروع: {start_date}, تاریخ پایان: {end_date}, مسیر خروجی: {output_dir}")

        collector = MarketDataCollector(
            request_delay=0.5,
            max_retries=3,
            max_workers=4
        )

        start_time = time.time()
        raw_data = collector.get_raw_market_data(start_date, end_date)

        logger.info("\n📊 خلاصه داده‌های دریافت شده:")
        for data_type, data in raw_data.items():
            if isinstance(data, dict):
                logger.info(f"  - {data_type}: {len(data)} نماد")
            elif isinstance(data, pd.DataFrame):
                logger.info(f"  - {data_type}: {len(data)} رکورد")
            else:
                logger.info(f"  - {data_type}: {data}")

        collector.save_data(raw_data, output_dir)

        execution_time = time.time() - start_time
        logger.info(f"\n⏱️ زمان کل اجرا: {execution_time:.2f} ثانیه")

    except Exception as e:
        logger.error(f"❌ خطای کلی برنامه: {str(e)}")
        raise


if __name__ == '__main__':
    main()

