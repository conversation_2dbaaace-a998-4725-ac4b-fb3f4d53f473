import warnings
import finpy_tse as fpy
import pandas as pd
from datetime import datetime, timedelta
import logging
import time
import jdatetime
from typing import Dict, List, Optional, Tuple

warnings.filterwarnings('ignore', category=FutureWarning)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def get_shamsi_date(delta_days: int = 0) -> str:
    """تبدیل تاریخ میلادی به شمسی با فرمت مورد نیاز"""
    today = datetime.now()
    target_date = today - timedelta(days=delta_days)
    j_date = jdatetime.date.fromgregorian(date=target_date.date())
    return j_date.strftime("%Y-%m-%d")

def format_number(number: float) -> str:
    """تبدیل اعداد به فرمت خوانا با جداکننده هزارگان"""
    if number >= 1_000_000_000:
        return f"{number/1_000_000_000:.2f} میلیارد"
    elif number >= 1_000_000:
        return f"{number/1_000_000:.2f} میلیون"
    else:
        return f"{number:,.0f}"

def get_symbol_history(symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
    """دریافت تاریخچه یک نماد با مدیریت خطا"""
    logger.info(f"دریافت تاریخچه {symbol}...")
    
    for attempt in range(3):  # سه بار تلاش
        try:
            history = fpy.Get_Price_History(
                stock=symbol,
                start_date=start_date,
                end_date=end_date,
                adjust_price=True,
                show_weekday=False,
                double_date=False
            )
            
            if isinstance(history, tuple):
                history = history[0]
            
            if isinstance(history, pd.DataFrame) and not history.empty:
                # اطمینان از وجود ستون‌های اصلی
                required_cols = ['Date', 'Close', 'Volume']
                if all(col in history.columns for col in required_cols):
                    logger.info(f"✅ داده‌های {symbol} با موفقیت دریافت شد")
                    return history
                else:
                    logger.warning(f"ستون‌های ضروری برای {symbol} موجود نیست")
            else:
                logger.warning(f"داده‌های نامعتبر برای {symbol}")
                
            time.sleep(5)  # تاخیر بین تلاش‌ها
                
        except Exception as e:
            logger.error(f"❌ خطا در دریافت تاریخچه {symbol} (تلاش {attempt + 1}): {str(e)}")
            time.sleep(5)
    
    return None

def calculate_trading_stats(df: pd.DataFrame) -> Dict:
    """محاسبه آمار معاملات با اطلاعات بیشتر"""
    price_change = (df['Close'].iloc[-1] / df['Close'].iloc[0] - 1) * 100
    avg_price = df['Close'].mean()
    return {
        'trading_days': len(df),
        'volume_mean': df['Volume'].mean(),
        'value_mean': df['Value'].mean(),
        'price_change': price_change,
        'avg_price': avg_price,
        'max_price': df['High'].max(),
        'min_price': df['Low'].min(),
        'total_trades': df['No'].sum() if 'No' in df.columns else 0
    }

def log_trading_stats(symbol: str, stats: Dict) -> None:
    """نمایش آمار معاملات با جزئیات بیشتر"""
    price_status = "📈" if stats['price_change'] > 0 else "📉"
    logger.info(f"""✅ {symbol}:
    • تعداد روزهای معاملاتی: {stats['trading_days']} روز
    • میانگین حجم معاملات: {format_number(stats['volume_mean'])}
    • میانگین ارزش معاملات: {format_number(stats['value_mean'])} ریال
    • تغییر قیمت: {price_status} {stats['price_change']:.1f}%
    • قیمت میانگین: {format_number(stats['avg_price'])} ریال
    • بیشترین قیمت: {format_number(stats['max_price'])} ریال
    • کمترین قیمت: {format_number(stats['min_price'])} ریال
    • تعداد کل معاملات: {format_number(stats['total_trades'])}
""")

def test_market_connection(top_n: int = 3, days_back: int = 30, min_success_rate: float = 50.0) -> bool:
    """
    تست اتصال به سرویس بازار و دریافت داده‌های نمادهای برتر
    
    Args:
        top_n: تعداد نمادهای برتر
        days_back: تعداد روز قبل برای دریافت تاریخچه
        min_success_rate: حداقل درصد موفقیت قابل قبول
    """
    try:
        start_time = time.time()
        logger.info("تلاش برای دریافت دیده‌بان بازار...")
        market_watch = fpy.Get_MarketWatch(save_excel=False)
        
        if isinstance(market_watch, tuple):
            market_watch = market_watch[0]
            
        if not market_watch.empty:
            logger.info("✅ دریافت دیده‌بان بازار موفق بود")
            logger.info(f"تعداد نمادها: {len(market_watch):,}")
            
            # مرتب‌سازی بر اساس ارزش معاملات به جای حجم
            active_symbols = market_watch.sort_values('Value', ascending=False).index[:top_n].tolist()
            logger.info("\n=== آمار نمادهای پرمعامله ===")
            
            end_date = get_shamsi_date(0)
            start_date = get_shamsi_date(days_back)
            logger.info(f"بازه زمانی: از {start_date} تا {end_date}\n")
            
            success_count = sum(1 for symbol in active_symbols 
                              if get_symbol_history(symbol, start_date, end_date) is not None)
            
            success_rate = (success_count / len(active_symbols)) * 100
            elapsed_time = time.time() - start_time
            
            logger.info(f"\n=== نتیجه نهایی ===")
            logger.info(f"✅ {success_count} از {len(active_symbols)} نماد با موفقیت دریافت شد ({success_rate:.1f}%)")
            logger.info(f"⏱ زمان اجرا: {elapsed_time:.1f} ثانیه")
            
            return success_rate >= min_success_rate
            
        else:
            logger.error("❌ دیده‌بان بازار خالی است")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطا در اتصال به سرویس: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_market_connection(top_n=3, days_back=30, min_success_rate=50.0)
    if not success:
        exit(1)
