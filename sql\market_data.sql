CREATE TABLE market_data (
    -- Primary Keys
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(30) NOT NULL,
    date DATE NOT NULL,

    -- Price Data
    open DECIMAL(20,2),
    high DECIMAL(20,2),
    low DECIMAL(20,2),
    close DECIMAL(20,2),
    adj_close DECIMAL(20,2),
    volume BIGINT,

    -- Trading Data
    value DECIMAL(20,2),
    trades_count INTEGER,
    
    -- Technical Indicators
    sma_20 DECIMAL(20,2),
    ema_20 DECIMAL(20,2),
    rsi_14 DECIMAL(20,2),
    macd DECIMAL(20,2),
    macd_signal DECIMAL(20,2),
    macd_hist DECIMAL(20,2),
    bb_upper DECIMAL(20,2),
    bb_middle DECIMAL(20,2),
    bb_lower DECIMAL(20,2),

    -- Fundamental Data
    pe_ratio DECIMAL(20,2),
    eps DECIMAL(20,2),
    market_cap DECIMAL(20,2),
    free_float DECIMAL(20,2),

    -- Market Data
    market_index DECIMAL(20,2),
    industry_index DECIMAL(20,2),

    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    UNIQUE(symbol, date)
);

-- Index for faster queries
CREATE INDEX idx_market_data_symbol_date ON market_data(symbol, date);
CREATE INDEX idx_market_data_date ON market_data(date);

ALTER TABLE market_data ADD COLUMN (
    -- نسبت‌های مالی
    p_b_ratio DECIMAL(20,2),      -- نسبت قیمت به ارزش دفتری
    debt_to_equity DECIMAL(20,2),  -- نسبت بدهی به حقوق صاحبان سهام
    current_ratio DECIMAL(20,2),   -- نسبت جاری
    quick_ratio DECIMAL(20,2),     -- نسبت آنی
    
    -- داده‌های صورت‌های مالی
    revenue DECIMAL(20,2),         -- درآمد
    net_profit DECIMAL(20,2),      -- سود خالص
    operating_profit DECIMAL(20,2), -- سود عملیاتی
    
    -- داده‌های صنعت
    industry_pe DECIMAL(20,2),     -- P/E صنعت
    industry_rank INTEGER,         -- رتبه در صنعت
    
    -- داده‌های معاملاتی پیشرفته
    buy_power DECIMAL(20,2),       -- قدرت خرید
    sell_pressure DECIMAL(20,2),   -- فشار فروش
    institutional_ownership DECIMAL(20,2) -- درصد مالکیت نهادی
);
