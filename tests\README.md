tests/
├── unit/                     
│   ├── conftest.py
│   ├── test_data_quality.py
│   ├── test_preprocessing.py
│   └── test_ingestion.py
├── integration/             
│   ├── test_finpy.py
│   ├── test_finpy_simple.py
│   ├── test_integration.py
│   ├── test_pipeline.py
│   └── test_real_components.py
├── system/                  
│   ├── test_config.py
│   ├── test_trading_system.py
│   └── manual_config_test.py
├── performance/            
│   ├── test_monitoring.py
│   └── test_optimize_indicators.py
├── mocks/                  
│   ├── broker_responses.py
│   ├── mock_components.py
│   ├── mock_trading.py
│   └── __init__.py
└── .github/
    └── workflows/
        ├── ci.yml
        └── test.yml
