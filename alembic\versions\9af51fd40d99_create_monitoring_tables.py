"""create_monitoring_tables

Revision ID: 9af51fd40d99
Revises: 
Create Date: 2025-03-04 01:47:26.450070

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9af51fd40d99'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('alerts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('severity', sa.String(), nullable=False),
    sa.Column('message', sa.String(), nullable=False),
    sa.Column('source', sa.String(), nullable=True),
    sa.Column('is_resolved', sa.<PERSON>(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.Column('resolution_note', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_alerts_id'), 'alerts', ['id'], unique=False)
    op.create_table('system_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('cpu_usage', sa.Float(), nullable=True),
    sa.Column('memory_usage', sa.Float(), nullable=True),
    sa.Column('disk_usage', sa.Float(), nullable=True),
    sa.Column('network_latency', sa.Float(), nullable=True),
    sa.Column('active_connections', sa.Integer(), nullable=True),
    sa.Column('error_count', sa.Integer(), nullable=True),
    sa.Column('performance_metrics', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_metrics_id'), 'system_metrics', ['id'], unique=False)
    op.drop_table('daily_prices')
    op.drop_table('stocks')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stocks',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('stocks_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('symbol', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='stocks_pkey'),
    sa.UniqueConstraint('symbol', name='stocks_symbol_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('daily_prices',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stock_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('trade_date', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('open_price', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('close_price', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('high', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('low', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('volume', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('rsi', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=True),
    sa.Column('ma', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('high_price', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('low_price', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('ma20', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('macd', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('signal', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('STD20', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('bollinger_upper', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('bollinger_lower', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('stock_symbol', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], name='daily_prices_stock_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='daily_prices_pkey'),
    sa.UniqueConstraint('stock_id', 'trade_date', name='daily_prices_stock_id_trade_date_key')
    )
    op.drop_index(op.f('ix_system_metrics_id'), table_name='system_metrics')
    op.drop_table('system_metrics')
    op.drop_index(op.f('ix_alerts_id'), table_name='alerts')
    op.drop_table('alerts')
    # ### end Alembic commands ###
