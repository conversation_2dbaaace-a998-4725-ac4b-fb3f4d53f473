from dependency_injector import containers, providers
from .external.market.market_data import MarketDataCollector
from .database.market_storage import MarketStorage
from .database import DatabaseSettings

class Container(containers.DeclarativeContainer):
    """کانتینر وابستگی‌های برنامه"""
    
    config = providers.Configuration()
    
    # تنظیمات دیتابیس
    db_settings = providers.Singleton(
        DatabaseSettings,
        url=config.database.url
    )
    
    # سرویس‌های خارجی
    market_data_collector = providers.Singleton(
        MarketDataCollector
    )
    
    # ذخیره‌سازی
    market_storage = providers.Singleton(
        MarketStorage,
        db_url=config.database.url
    )