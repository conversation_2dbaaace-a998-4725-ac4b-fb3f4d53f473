bourse_pipeline/├── domain/
│   ├── entities/│   │   ├── __init__.py
│   │   ├── market_data.py      # کلاس‌های پایه داده‌های بازار│   │   ├── trade.py           # کلاس‌های معاملات
│   │   └── portfolio.py       # کلاس‌های پورتفولیو│   ├── interfaces/
│   │   ├── __init__.py│   │   ├── repository.py      # اینترفیس‌های ذخیره‌سازی
│   │   └── market_gateway.py  # اینترفیس‌های دسترسی به بازار│   └── __init__.py
├── application/│   ├── services/
│   │   ├── __init__.py│   │   ├── market_data_service.py
│   │   └── trading_service.py│   ├── analysis/
│   │   ├── __init__.py│   │   ├── technical.py
│   │   └── fundamental.py│   └── __init__.py
├── infrastructure/│   ├── repositories/
│   │   ├── __init__.py│   │   ├── postgres_repository.py
│   │   └── cache_repository.py│   ├── gateways/
│   │   ├── __init__.py│   │   ├── tsetmc_gateway.py
│   │   └── finpy_gateway.py│   └── __init__.py
└── interface/    ├── api/
    │   ├── __init__.py    │   ├── rest/
    │   └── websocket/    ├── cli/
    │   ├── __init__.py
    │   └── commands.py



















# اطلاعات پروژه