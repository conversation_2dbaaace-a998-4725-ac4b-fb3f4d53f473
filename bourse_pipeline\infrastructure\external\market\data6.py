import sys
import asyncio
import time
import random
import logging
import argparse
import json
from pathlib import Path
from datetime import datetime
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import configparser
from contextlib import contextmanager

import pandas as pd
import joblib
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import finpy_tse as fpy

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# پیکربندی logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# خواندن تنظیمات از فایل config.ini
config = configparser.ConfigParser()
config_path = Path(__file__).parent / "config.ini"
logger.info(f"Reading config from: {config_path}")
if not config_path.exists():
    logger.error(f"Config file not found at: {config_path}")
    sys.exit(1)
config.read(config_path)

try:
    DB_USER = config.get("database", "DB_USER")
    DB_PASSWORD = config.get("database", "DB_PASSWORD")
    DB_HOST = config.get("database", "DB_HOST")
    DB_PORT = config.get("database", "DB_PORT")
    DB_NAME = config.get("database", "DB_NAME")
except Exception as e:
    logger.error(f"Error reading database settings: {e}")
    sys.exit(1)

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
MAX_WORKERS = config.getint("general", "max_workers", fallback=8)
BATCH_SIZE = config.getint("general", "batch_size", fallback=20)
REQUEST_DELAY = config.getfloat("general", "request_delay", fallback=0.5)

# تابع تبدیل تاریخ بهینه‌شده
def convert_date(date_str: str):
    try:
        date = pd.to_datetime(date_str, errors='coerce')
        return date if not pd.isna(date) else None
    except Exception as e:
        logger.error(f"Date conversion error for {date_str}: {e}")
        return None

# ایجاد engine پایگاه داده
def get_db_engine():
    try:
        engine = create_engine(DATABASE_URL)
        logger.info("اتصال به پایگاه داده برقرار شد.")
        return engine
    except SQLAlchemyError as e:
        logger.error(f"خطا در اتصال به پایگاه داده: {e}")
        raise

@contextmanager
def get_db_connection():
    engine = get_db_engine()
    connection = engine.connect()
    try:
        yield connection
    finally:
        connection.close()

# تست اتصال پایگاه داده
def test_db_connection():
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info("✅ اتصال به پایگاه داده موفقیت‌آمیز بود")
            return True
    except Exception as e:
        logger.error(f"❌ خطا در اتصال به پایگاه داده: {e}")
        return False

# دریافت لیست نمادهای بازار به صورت دیکشنری
def get_market_stocks() -> dict:
    logger.info("در حال دریافت لیست سهام بازار...")
    try:
        stock_list = fpy.Build_Market_StockList(
            bourse=True,
            farabourse=True,
            payeh=True,
            detailed_list=False,
            show_progress=True,
            save_excel=False,
            save_csv=False
        )
        if stock_list is None or stock_list.empty:
            logger.error("❌ لیست سهام دریافت شده خالی است.")
            return {}
        ticker_map = {}
        symbol_col = 'Name' if 'Name' in stock_list.columns else stock_list.columns[0]
        for ticker in stock_list[symbol_col]:
            norm = ticker.strip()
            ticker_map[norm] = ticker
        logger.info(f"📊 تعداد نمادها: {len(ticker_map):,}")
        return ticker_map
    except Exception as e:
        logger.error(f"❌ خطا در دریافت لیست سهام: {e}")
        raise

# استخراج تاریخچه قیمت روزانه یک نماد
def get_stock_history(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    logger.info(f"استخراج تاریخچه قیمت برای {symbol}...")
    try:
        df = fpy.Get_Price_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            adjust_price=True,
            show_weekday=False,
            double_date=False
        )
        if df is None or df.empty:
            logger.warning(f"داده‌ای برای {symbol} دریافت نشد.")
            return pd.DataFrame()
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        available_cols = [col for col in required_columns if col in df.columns]
        df = df[available_cols].copy()
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        df['symbol'] = symbol
        df['EPS'] = df.get('EPS', pd.NA)
        return df
    except Exception as e:
        logger.error(f"خطا در استخراج تاریخچه قیمت برای {symbol}: {e}")
        raise

# استخراج اطلاعات حقیقی-حقوقی برای یک نماد
def extract_ri_history(symbol: str, start_date: str, end_date: str) -> dict:
    logger.info(f"استخراج اطلاعات حقیقی-حقوقی برای {symbol}...")
    try:
        df = fpy.Get_RI_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            show_weekday=False,
            double_date=False,
            alt=False
        )
        if df is not None and 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج ri_history برای {symbol}: {e}")
        return {}

# استخراج ریز معاملات برای یک نماد
def extract_intraday_trades(symbol: str, start_date: str, end_date: str) -> dict:
    logger.info(f"استخراج ریز معاملات برای {symbol}...")
    try:
        df = fpy.Get_IntradayTrades_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_trades برای {symbol}: {e}")
        return {}

# استخراج عمق بازار برای یک نماد در تاریخ مشخص
def extract_intraday_orderbook(symbol: str, date: str) -> dict:
    logger.info(f"استخراج عمق بازار برای {symbol} در تاریخ {date}...")
    try:
        df = fpy.Get_IntradayOB_History(
            stock=symbol,
            start_date=date,
            end_date=date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_orderbook برای {symbol}: {e}")
        return {}

# استخراج وضعیت صف معاملات برای یک نماد
def extract_queue_history(symbol: str, start_date: str, end_date: str) -> dict:
    logger.info(f"استخراج وضعیت صف معاملات برای {symbol}...")
    try:
        df = fpy.Get_Queue_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            show_per_capita=True,
            show_weekday=False,
            double_date=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج queue_history برای {symbol}: {e}")
        return {}

# استخراج داده‌های مختلف به‌صورت موازی و ادغام در یک دیکشنری
def fetch_all_data_parallel(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
    data = {}
    funcs = {
        "daily_price": lambda: get_stock_history(symbol, start_date, end_date).to_dict(orient="records"),
        "ri_history": lambda: extract_ri_history(symbol, start_date, end_date),
        "intraday_trades": lambda: extract_intraday_trades(symbol, start_date, end_date),
        "intraday_orderbook": lambda: extract_intraday_orderbook(symbol, orderbook_date),
        "queue_history": lambda: extract_queue_history(symbol, start_date, end_date)
    }
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_map = {executor.submit(func): key for key, func in funcs.items()}
        for future in as_completed(future_map):
            key = future_map[future]
            try:
                result = future.result()
                data[key] = result
            except Exception as ex:
                logger.error(f"خطا در استخراج {key} برای {symbol}: {ex}")
                data[key] = {}
    return data

# پردازش کامل یک نماد
def process_symbol(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
    logger.info(f"\n===== شروع پردازش {symbol} =====")
    try:
        # Get daily price data
        daily_price = get_stock_history(symbol, start_date, end_date)
        
        # Check if daily_price is a DataFrame and convert to dict if needed
        if isinstance(daily_price, pd.DataFrame):
            if daily_price.empty:
                logger.warning(f"⚠️ داده‌های قیمت روزانه برای {symbol} خالی است")
                daily_price_dict = {}
            else:
                daily_price_dict = daily_price.to_dict(orient="records")
        else:
            daily_price_dict = daily_price if daily_price else {}
        
        # Get other market data
        ri_history = extract_ri_history(symbol, start_date, end_date)
        intraday_trades = extract_intraday_trades(symbol, start_date, end_date)
        intraday_orderbook = extract_intraday_orderbook(symbol, orderbook_date)
        queue_history = extract_queue_history(symbol, start_date, end_date)
        
        # Create the result dictionary
        market_data = {
            "symbol": symbol,
            "extraction_date": datetime.now(),
            "market_data": {
                "daily_price": daily_price_dict,
                "ri_history": ri_history,
                "intraday_trades": intraday_trades,
                "intraday_orderbook": intraday_orderbook,
                "queue_history": queue_history
            }
        }
        
        # Validate data
        if not any(market_data["market_data"].values()):
            logger.warning(f"⚠️ هیچ داده‌ای برای {symbol} دریافت نشد")
            return None
            
        return market_data
    except Exception as e:
        logger.error(f"❌ خطا در پردازش {symbol}: {e}")
        return None

# پردازش تمامی نمادهای بازار به‌صورت دسته‌ای و موازی
def process_all_stocks(start_date: str, end_date: str, orderbook_date: str, max_workers: int = MAX_WORKERS) -> tuple:
    ticker_map = get_market_stocks()
    if not ticker_map:
        logger.error("❌ لیست نمادها دریافت نشد.")
        return 0, []
    tickers = list(ticker_map.keys())
    total_batches = (len(tickers) + BATCH_SIZE - 1) // BATCH_SIZE
    processed_count = 0
    failed_symbols = []
    logger.info(f"🎯 شروع پردازش {len(tickers)} نماد در {total_batches} دسته...")

    for batch_idx in range(total_batches):
        batch_tickers = tickers[batch_idx * BATCH_SIZE:(batch_idx + 1) * BATCH_SIZE]
        batch_data = []
        logger.info(f"\n📦 پردازش دسته {batch_idx + 1}/{total_batches} (پیشرفت: {processed_count}/{len(tickers)})")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_symbol, ticker, start_date, end_date, orderbook_date): ticker for ticker in batch_tickers}
            for future in as_completed(futures):
                symbol = futures[future]
                try:
                    data = future.result()
                    if data is not None:
                        batch_data.append(data)
                        processed_count += 1
                        logger.debug(f"✅ {symbol} با موفقیت پردازش شد")
                    else:
                        failed_symbols.append(symbol)
                        logger.warning(f"⚠️ {symbol} داده‌ای نداشت")
                except Exception as ex:
                    failed_symbols.append(symbol)
                    logger.error(f"❌ خطا در پردازش {symbol}: {ex}")
        # ذخیره هر دسته در دیتابیس
        if batch_data:
            try:
                save_to_postgresql(batch_data)
                logger.info(f"✅ دسته {batch_idx + 1} در دیتابیس ذخیره شد")
            except Exception as e:
                logger.error(f"❌ خطا در ذخیره دسته {batch_idx + 1}: {e}")
        # ذخیره وضعیت میانی
        save_intermediate_results(batch_idx)
        # تاخیر تصادفی جهت کاهش فشار به سرور
        time.sleep(random.uniform(REQUEST_DELAY, REQUEST_DELAY + 1.0))
    # اضافه کردن بررسی برای جلوگیری از تقسیم بر صفر
    if len(tickers) == 0:
        logger.error("❌ لیست نمادها خالی است.")
        return 0, []
        
    success_rate = (processed_count / len(tickers)) * 100 if len(tickers) > 0 else 0
    logger.info(
        f"پردازش تمامی نمادها به پایان رسید:\n"
        f"- تعداد نمادهای پردازش شده: {processed_count:,}\n"
        f"- درصد موفقیت: {success_rate:.1f}%\n"
        f"- تعداد خطا: {len(failed_symbols):,}\n"
        f"- نمادهای ناموفق: {', '.join(failed_symbols)}"
    )
    return processed_count, failed_symbols

# ذخیره نتایج میانی با استفاده از joblib
def save_intermediate_results(batch_id: int):
    try:
        cache_dir = Path("cache/intermediate")
        cache_dir.mkdir(parents=True, exist_ok=True)
        joblib.dump({}, cache_dir / f"batch_{batch_id}.joblib")
        logger.info(f"نتایج میانی دسته {batch_id} ذخیره شدند.")
    except Exception as e:
        logger.error(f"خطا در ذخیره نتایج میانی دسته {batch_id}: {e}")

# ایجاد جداول مورد نیاز در پایگاه داده
def create_tables(engine):
    try:
        with engine.connect() as conn:
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS market_unified_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    raw_data JSONB NOT NULL,
                    extraction_date TIMESTAMP NOT NULL
                )
            """))
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_market_unified_data_symbol
                ON market_unified_data(symbol)
            """))
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_market_unified_data_extraction_date
                ON market_unified_data(extraction_date)
            """))
            conn.commit()
            logger.info("✅ جداول و ایندکس‌ها با موفقیت ایجاد شدند.")
    except Exception as e:
        logger.error(f"❌ خطا در ایجاد جداول: {e}")
        raise

# ذخیره داده‌ها در PostgreSQL به صورت batch
def save_to_postgresql(data_list: list, table_name: str = "market_unified_data"):
    if not data_list:
        logger.error("❌ داده‌ای برای ذخیره وجود ندارد")
        return
    try:
        engine = get_db_engine()
        rows = []
        for data in data_list:
            if data is None:
                continue
            row = {
                "symbol": data["symbol"],
                "raw_data": json.dumps(data["market_data"], ensure_ascii=False, default=str),
                "extraction_date": data["extraction_date"]
            }
            rows.append(row)
        if not rows:
            logger.warning("❌ هیچ داده معتبری برای ذخیره وجود ندارد")
            return
        insert_query = f"""
    INSERT INTO {table_name} (symbol, raw_data, extraction_date)
    VALUES (%(symbol)s, %(raw_data)s, %(extraction_date)s)
    ON CONFLICT (symbol)
    DO UPDATE SET
        raw_data = EXCLUDED.raw_data,
        extraction_date = EXCLUDED.extraction_date
"""
        chunk_size = 50
        total = len(rows)
        for i in range(0, total, chunk_size):
            chunk = rows[i:i+chunk_size]
            try:
                with engine.begin() as conn:
                    conn.execute(text(insert_query), chunk)
                logger.info(f"✅ بخش {i//chunk_size + 1} از {(total + chunk_size - 1) // chunk_size} با موفقیت درج شد")
            except Exception as e:
                logger.error(f"❌ خطا در درج بخش {i//chunk_size + 1}: {e}")
                continue
    except Exception as e:
        logger.error(f"❌ خطا در ذخیره‌سازی داده‌ها: {e}")
        raise

# بازیابی داده‌های یک نماد از پایگاه داده
def get_symbol_data(symbol: str) -> dict:
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT raw_data, extraction_date
                FROM market_unified_data
                WHERE symbol = :symbol
            """), {"symbol": symbol})
            row = result.fetchone()
            if not row:
                return None
            return {
                "symbol": symbol,
                "market_data": row[0],
                "extraction_date": row[1]
            }
    except Exception as e:
        logger.error(f"❌ خطا در بازیابی داده‌های {symbol}: {e}")
        return None

# تست پردازش و ذخیره داده‌ها برای یک نماد
def test_data_processing():
    try:
        symbol = "فولاد"  # یا هر نماد دیگری
        start_date = "1402-01-01"
        end_date = "1402-01-10"
        orderbook_date = "1402-01-01"
        
        data = process_symbol(symbol, start_date, end_date, orderbook_date)
        
        # Check if data exists and has the expected structure
        if data is None:
            logger.error("❌ داده‌های پردازش شده برای تست خالی است")
            return False
            
        if not isinstance(data, dict) or 'market_data' not in data:
            logger.error("❌ ساختار داده‌های پردازش شده نامعتبر است")
            return False
            
        # Check if we have any data to save
        if not any(data['market_data'].values()):
            logger.error("❌ هیچ داده بازاری برای ذخیره وجود ندارد")
            return False
            
        # Save to database
        save_to_postgresql([data])
        
        # Verify data was saved
        engine = get_db_engine()
        with engine.connect() as conn:
            result = conn.execute(text(
                "SELECT COUNT(*) FROM market_unified_data WHERE symbol = :symbol"
            ), {"symbol": symbol})
            count = result.scalar()
            
        if count > 0:
            logger.info(f"✅ تست موفق: داده‌های {symbol} با موفقیت ذخیره شد")
            return True
        else:
            logger.error("❌ تست ناموفق: داده‌ها در دیتابیس یافت نشد")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطا در تست: {e}")
        return False

# تابع اصلی برنامه
def main():
    start_date = "1402-01-01"      # تاریخ شروع
    end_date = "1402-12-29"        # تاریخ پایان
    orderbook_date = "1402-12-29"   # تاریخ عمق بازار
    max_workers = 5               # تعداد workers برای پردازش موازی

    if not test_db_connection():
        logger.error("❌ خطا در اتصال به پایگاه داده")
        sys.exit(1)

    engine = get_db_engine()
    create_tables(engine)

    if test_data_processing():
        logger.info("✅ تست اولیه موفق بود. شروع پردازش کل بازار...")
        processed_count, failed_symbols = process_all_stocks(
            start_date=start_date,
            end_date=end_date,
            orderbook_date=orderbook_date,
            max_workers=max_workers
        )
        if processed_count > 0:
            logger.info("✅ پردازش کل بازار با موفقیت به پایان رسید")
        else:
            logger.error("❌ خطا در پردازش بازار")
    else:
        logger.error("❌ تست اولیه ناموفق بود. لطفا مشکلات را بررسی کنید.")
        sys.exit(1)

if __name__ == '__main__':
    main()

def retry_operation(func, max_retries=3, delay=1):
    def wrapper(*args, **kwargs):
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"خطا در اجرای {func.__name__}: {e}. تلاش مجدد {attempt+1}/{max_retries}")
                time.sleep(delay * (2 ** attempt))  # exponential backoff
    return wrapper

# استفاده:
get_stock_history_with_retry = retry_operation(get_stock_history)
