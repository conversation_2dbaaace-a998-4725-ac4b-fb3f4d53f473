from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime, date

class SystemSettings(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    name: str = "BourseSystem"
    version: str = "1.0.0"
    mode: str = "live"
    debug: bool = False

class MonitoringSettings(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    enabled: bool = True
    log_level: str = "INFO"
    metrics_interval: int = 60
    max_workers: int = 4

class DatabaseConfig(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    url: str
    pool_size: int = 20
    max_overflow: int = 10
    echo: bool = False

class DataIngestionConfig(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    stocks_list_file: str
    default_start_date: str
    default_end_date: str
    cache_enabled: bool = True
    cache_ttl: int = 3600

class MarketDataSettings(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    provider: str = "finpy_tse"
    api_key: Optional[str] = None
    cache_enabled: bool = True
    cache_duration: int = 300

class TechnicalIndicatorConfig(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    enabled: Dict[str, bool] = Field(default_factory=lambda: {
        "moving_average": True,
        "rsi": True,
        "macd": True,
        "bollinger_bands": True
    })
    parameters: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

class SystemConfig(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    system: SystemSettings
    monitoring: MonitoringSettings
    market_data: MarketDataSettings
    database: DatabaseConfig
    data_ingestion: DataIngestionConfig
    technical_indicators: TechnicalIndicatorConfig

    def __init__(self, **data):
        # Convert dictionaries to appropriate model instances
        if "system" in data and isinstance(data["system"], dict):
            data["system"] = SystemSettings(**data["system"])
        if "monitoring" in data and isinstance(data["monitoring"], dict):
            data["monitoring"] = MonitoringSettings(**data["monitoring"])
        if "market_data" in data and isinstance(data["market_data"], dict):
            data["market_data"] = MarketDataSettings(**data["market_data"])
        if "database" in data and isinstance(data["database"], dict):
            data["database"] = DatabaseConfig(**data["database"])
        if "data_ingestion" in data and isinstance(data["data_ingestion"], dict):
            data["data_ingestion"] = DataIngestionConfig(**data["data_ingestion"])
        if "technical_indicators" in data and isinstance(data["technical_indicators"], dict):
            data["technical_indicators"] = TechnicalIndicatorConfig(**data["technical_indicators"])
        
        super().__init__(**data)
        self._start_date: Optional[date] = None
        self._end_date: Optional[date] = None
        self._init_dates()

    def _init_dates(self) -> None:
        try:
            self._start_date = datetime.strptime(
                self.data_ingestion.default_start_date, 
                '%Y-%m-%d'
            ).date()
            
            self._end_date = datetime.strptime(
                self.data_ingestion.default_end_date,
                '%Y-%m-%d'
            ).date()
            
            if self._start_date >= self._end_date:
                raise ValueError("Start date must be before end date")
                
        except ValueError as e:
            self._start_date = date(1400, 1, 1)
            self._end_date = date(1402, 12, 29)

    @property
    def start_date(self) -> date:
        return self._start_date

    @property
    def end_date(self) -> date:
        return self._end_date

    @property
    def app_name(self) -> str:
        return self.system.name
    
    @property
    def app_version(self) -> str:
        return self.system.version
    
    @property
    def database_url(self) -> str:
        return self.database.url

    def get_indicator_params(self, indicator_name: str) -> Dict[str, Any]:
        return self.technical_indicators.parameters.get(indicator_name, {})

    def to_dict(self) -> Dict[str, Any]:
        return {
            'system': self.system.model_dump(),
            'monitoring': self.monitoring.model_dump(),
            'market_data': self.market_data.model_dump(),
            'database': self.database.model_dump(),
            'data_ingestion': self.data_ingestion.model_dump(),
            'technical_indicators': self.technical_indicators.model_dump()
        }
