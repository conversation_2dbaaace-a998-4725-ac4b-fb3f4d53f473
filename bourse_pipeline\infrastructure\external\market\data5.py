import sys
import asyncio
import time
import random
import logging
import argparse
import json
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import configparser

import pandas as pd
import joblib
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import finpy_tse as fpy
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# پیکربندی logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# خواندن تنظیمات از فایل config
config = configparser.ConfigParser()
config.read('config.ini')

# تنظیمات دیتابیس
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "raw_dat"  # اطمینان حاصل کنید که این دیتابیس وجود دارد
DB_USER = "postgres"
DB_PASSWORD = "Honey33454"

def get_db_engine():
    """ایجاد engine پایگاه داده با استفاده از SQLAlchemy."""
    try:
        database_url = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        engine = create_engine(database_url, echo=True)  # echo=True برای دیدن SQL queries
        return engine
    except Exception as e:
        logger.error(f"خطا در ایجاد engine دیتابیس: {str(e)}")
        raise

def test_db_connection():
    """تست اتصال به پایگاه داده"""
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info("✅ اتصال به پایگاه داده موفقیت‌آمیز بود")
            return True
    except Exception as e:
        logger.error(f"❌ خطا در اتصال به پایگاه داده: {str(e)}")
        return False

def get_market_stocks() -> dict:
    """
    دریافت لیست نمادهای بازار به صورت دیکشنری: normalized -> original.
    از منطق انتخاب ستون مشابه اسکریپت CSV استفاده می‌شود.
    """
    logger.info("در حال دریافت لیست سهام بازار...")
    try:
        stock_list = fpy.Build_Market_StockList(
            bourse=True,
            farabourse=True,
            payeh=True,
            detailed_list=True,   # استفاده از لیست جامع
            show_progress=True,
            save_excel=False,
            save_csv=False
        )
        if stock_list is None or stock_list.empty:
            logger.error("❌ لیست سهام دریافت شده خالی است.")
            return {}
        logger.info("✅ لیست سهام بازار با موفقیت دریافت شد.")
        
        # تعیین ستون مناسب برای نماد
        possible_cols = ['نماد', 'Name', 'Ticker(4)']
        symbol_col = None
        for col in possible_cols:
            if col in stock_list.columns:
                symbol_col = col
                break
        if symbol_col is None:
            symbol_col = stock_list.columns[0]
        
        ticker_map = {}
        for ticker in stock_list[symbol_col]:
            norm = str(ticker).strip()
            ticker_map[norm] = ticker  # نگهداری نام اصلی
        logger.info(f"📊 تعداد نمادها: {len(ticker_map):,}")
        return ticker_map
    except Exception as e:
        logger.error(f"❌ خطا در دریافت لیست سهام: {e}")
        raise DataExtractionError(e)

def get_stock_history(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    استخراج تاریخچه قیمت روزانه یک نماد.
    - ستون‌های اصلی: Date, Open, High, Low, Close, Volume.
    - ستون Date به datetime تبدیل می‌شود.
    - ستون EPS در صورت موجود بودن افزوده می‌شود؛ در غیر این صورت مقدار NaN.
    """
    logger.info(f"استخراج تاریخچه قیمت برای {symbol}...")
    try:
        # تغییر adjust_price به True مانند اسکریپت CSV
        df = fpy.Get_Price_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            adjust_price=True,
            show_weekday=False,
            double_date=False
        )
        if df is None or df.empty:
            logger.warning(f"داده‌ای برای {symbol} دریافت نشد.")
            return pd.DataFrame()
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        available_cols = [col for col in required_columns if col in df.columns]
        df = df[available_cols].copy()
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            df.rename(columns=lambda x: x.lower(), inplace=True)
        df['symbol'] = symbol
        df['eps'] = df.get('eps', pd.NA)
        return df
    except Exception as e:
        logger.error(f"خطا در استخراج تاریخچه قیمت برای {symbol}: {e}")
        raise DataExtractionError(e)

def extract_ri_history(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج اطلاعات حقیقی-حقوقی برای یک نماد."""
    logger.info(f"استخراج اطلاعات حقیقی-حقوقی برای {symbol}...")
    try:
        df = fpy.Get_RI_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            ignore_date=False,
            show_weekday=False,
            double_date=False,
            alt=False
        )
        if df is not None and 'Date' in df.columns:
            if not pd.api.types.is_datetime64_any_dtype(df['Date']):
                try:
                    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
                except Exception as conv_err:
                    logger.error(f"خطا در تبدیل تاریخ برای {symbol}: {conv_err}")
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج ri_history برای {symbol}: {e}")
        return {}

def extract_intraday_trades(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج ریز معاملات برای یک نماد."""
    logger.info(f"استخراج ریز معاملات برای {symbol}...")
    try:
        df = fpy.Get_IntradayTrades_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_trades برای {symbol}: {e}")
        return {}

def extract_intraday_orderbook(symbol: str, date: str) -> dict:
    """استخراج عمق بازار برای یک نماد در تاریخ مشخص."""
    logger.info(f"استخراج عمق بازار برای {symbol} در تاریخ {date}...")
    try:
        df = fpy.Get_IntradayOB_History(
            stock=symbol,
            start_date=date,
            end_date=date,
            jalali_date=True,
            combined_datatime=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج intraday_orderbook برای {symbol}: {e}")
        return {}

def extract_queue_history(symbol: str, start_date: str, end_date: str) -> dict:
    """استخراج وضعیت صف معاملات برای یک نماد."""
    logger.info(f"استخراج وضعیت صف معاملات برای {symbol}...")
    try:
        df = fpy.Get_Queue_History(
            stock=symbol,
            start_date=start_date,
            end_date=end_date,
            show_per_capita=True,
            show_weekday=False,
            double_date=False,
            show_progress=True
        )
        return df.to_dict(orient="records") if df is not None and not df.empty else {}
    except Exception as e:
        logger.error(f"خطا در استخراج queue_history برای {symbol}: {e}")
        return {}

def fetch_all_data_parallel(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
    """
    استخراج داده‌های مختلف (تاریخچه قیمت، ri_history، ریز معاملات، عمق بازار و وضعیت صف معاملات)
    برای یک نماد به صورت موازی و ادغام آن‌ها در یک دیکشنری.
    """
    data = {}
    funcs = {
        "daily_price": lambda: get_stock_history(symbol, start_date, end_date).to_dict(orient="records"),
        "ri_history": lambda: extract_ri_history(symbol, start_date, end_date),
        "intraday_trades": lambda: extract_intraday_trades(symbol, start_date, end_date),
        "intraday_orderbook": lambda: extract_intraday_orderbook(symbol, orderbook_date),
        "queue_history": lambda: extract_queue_history(symbol, start_date, end_date)
    }
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_map = {executor.submit(func): key for key, func in funcs.items()}
        for future in as_completed(future_map):
            key = future_map[future]
            try:
                result = future.result()
                data[key] = result
            except Exception as ex:
                logger.error(f"خطا در استخراج {key} برای {symbol}: {ex}")
                data[key] = {}
    return data

def process_symbol(symbol: str, start_date: str, end_date: str, orderbook_date: str) -> dict:
    """
    پردازش کامل یک نماد:
      - استخراج داده‌های روزانه (قیمت) جهت ادغام در DataFrame یکپارچه.
      - استخراج سایر داده‌ها به صورت دیکشنری (برای ذخیره به عنوان raw_data).
    """
    logger.info(f"\n===== شروع پردازش {symbol} =====")
    price_df = get_stock_history(symbol, start_date, end_date)
    extra_data = fetch_all_data_parallel(symbol, start_date, end_date, orderbook_date)
    logger.info(f"===== پایان پردازش {symbol} =====\n")
    return {
        "symbol": symbol,
        "price_data": price_df,
        "raw_data": extra_data,
        "extraction_date": datetime.now()
    }

def process_all_stocks(start_date: str, end_date: str, orderbook_date: str, max_workers: int = MAX_WORKERS) -> list:
    """
    پردازش تمامی نمادها:
      - استخراج داده‌های هر نماد به صورت موازی در دسته‌های (batch).
      - بازگشت لیستی از دیکشنری‌های مربوط به هر نماد جهت ذخیره در پایگاه داده.
    """
    ticker_map = get_market_stocks()
    if not ticker_map:
        logger.error("لیست نمادها دریافت نشد.")
        return []

    all_data = []
    tickers = list(ticker_map.keys())
    total_batches = (len(tickers) + BATCH_SIZE - 1) // BATCH_SIZE
    processed_count = 0
    failed_symbols = []

    for batch_idx in range(total_batches):
        batch_tickers = tickers[batch_idx * BATCH_SIZE:(batch_idx + 1) * BATCH_SIZE]
        logger.info(f"\n📦 پردازش دسته {batch_idx + 1}/{total_batches} (پیشرفت: {processed_count}/{len(tickers)})")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_symbol, ticker, start_date, end_date, orderbook_date): ticker for ticker in batch_tickers}
            for future in as_completed(futures):
                symbol = futures[future]
                try:
                    data = future.result()
                    all_data.append(data)
                    processed_count += 1
                except Exception as ex:
                    failed_symbols.append(symbol)
                    logger.error(f"خطا در پردازش {symbol}: {ex}")
        save_intermediate_results(batch_idx)
        time.sleep(2)

    success_rate = (processed_count / len(tickers)) * 100
    logger.info(f"✅ پردازش تمام نمادها به پایان رسید:\n"
                f"- تعداد کل نمادها: {len(tickers)}\n"
                f"- تعداد موفق: {processed_count}\n"
                f"- درصد موفقیت: {success_rate:.1f}%\n"
                f"- نمادهای ناموفق: {', '.join(failed_symbols)}")
    
    return all_data

def save_intermediate_results(batch_id: int):
    """ذخیره نتایج میانی به کمک joblib (اختیاری)."""
    try:
        cache_dir = Path("cache/intermediate")
        cache_dir.mkdir(parents=True, exist_ok=True)
        joblib.dump({}, cache_dir / f"batch_{batch_id}.joblib")
        logger.info(f"نتایج میانی دسته {batch_id} ذخیره شدند.")
    except Exception as e:
        logger.error(f"خطا در ذخیره نتایج میانی دسته {batch_id}: {e}")

def create_tables(engine):
    """ایجاد جداول مورد نیاز در پایگاه داده."""
    try:
        with engine.begin() as conn:
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS market_unified_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    raw_data JSONB NOT NULL,
                    extraction_date TIMESTAMP NOT NULL
                );
            """))
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_symbol 
                ON market_unified_data(symbol);
            """))
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_extraction_date 
                ON market_unified_data(extraction_date);
            """))
        logger.info("✅ جداول و ایندکس‌ها با موفقیت ایجاد شدند.")
    except Exception as e:
        logger.error(f"❌ خطا در ایجاد جداول: {e}")
        raise

def insert_market_data(engine, data_list):
    """درج داده‌های بازار در دیتابیس"""
    try:
        # استفاده از CAST به جای ::
        insert_query = """
            INSERT INTO market_unified_data
            (symbol, raw_data, extraction_date)
            VALUES (%(symbol)s, CAST(%(raw_data)s AS jsonb), %(extraction_date)s)
        """
        
        with engine.connect() as conn:
            for data in data_list:
                conn.execute(text(insert_query), {
                    'symbol': data['symbol'],
                    'raw_data': json.dumps(data['raw_data']),  # تبدیل به JSON string
                    'extraction_date': data['extraction_date']
                })
            conn.commit()
            
        logger.info("✅ داده‌ها با موفقیت در دیتابیس ذخیره شدند")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطا در درج داده‌ها: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="دریافت و ذخیره داده‌های بازار سهام")
    parser.add_argument("start_date", help="تاریخ شروع (مثال: 1403-01-01)")
    parser.add_argument("end_date", help="تاریخ پایان (مثال: 1403-01-07)")
    parser.add_argument("--orderbook_date", help="تاریخ عمق بازار", default=None)
    parser.add_argument("--max_workers", type=int, default=4, help="تعداد workers موازی")
    args = parser.parse_args()

    try:
        if not test_db_connection():
            logger.error("❌ خطا در اتصال به پایگاه داده")
            return

        engine = get_db_engine()
        create_tables(engine)

        all_data = process_all_stocks(
            start_date=args.start_date,
            end_date=args.end_date,
            orderbook_date=args.orderbook_date or args.start_date,
            max_workers=args.max_workers
        )

        if not all_data:
            logger.error("❌ هیچ داده‌ای دریافت نشد")
            return

        insert_market_data(engine, all_data)
        logger.info("✅ عملیات با موفقیت به پایان رسید")

    except Exception as e:
        logger.error(f"❌ خطای کلی در اجرای برنامه: {e}")
        raise

if __name__ == '__main__':
    main()
