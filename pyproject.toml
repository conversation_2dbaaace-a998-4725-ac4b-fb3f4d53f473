[tool.poetry]
name = "bourse-pipeline"
version = "0.1.0"
description = "A data pipeline for processing and analyzing stock market data."
authors = ["Your Name <<EMAIL>>"]
package-mode = true
readme = "README.md"
packages = [{include = "bourse_pipeline"}]

[[tool.poetry.source]]
name = "torch"
url = "https://download.pytorch.org/whl/cpu"
priority = "supplemental"

[tool.poetry.dependencies]
python = "^3.10"
dependency-injector = "^4.41.0"
colorama = "^0.4.6"
aioconsole = "^0.8.1"
psutil = "^7.0.0"
finpy-tse = "^1.2.10"
SQLAlchemy = {extras = ["asyncio"], version = "^2.0.39"}
python-dotenv = "^1.0.1"
PyYAML = "^6.0.1"
optuna = "^3.5.0"
requests = "^2.31.0"
logging-tree = "^1.9"
fastapi = "^0.109.2"
uvicorn = "^0.27.1"
pydantic = "^2.6.0"
Jinja2 = "^3.1.3"
joblib = "*"
psycopg2-binary = "2.9.9"
multimethod = "1.8.0"
scikit-learn = "^1.6.1"
statsmodels = "^0.14.4"
pandas-ta = "^0.3.14b0"
ta-lib = { path = "./ta_lib-0.6.0-cp310-cp310-win_amd64.whl" }
pandera = "^0.23.0"
pandas = ">=2.1.1"
alembic = "^1.14.1"
seaborn = "^0.13.2"
pydantic-settings = "^2.1.0"
jdatetime = "^5.2.0"
torch = { version = "2.1.0+cpu", source = "torch" }
numpy = ">=1.24.4,<2.0.0"
aioresponses = "^0.7.8"
plotly = "^5.19.0"
asyncpg = "^0.30.0"
matplotlib = ">=3.4,!=3.6.0,!=3.6.1"
scipy = ">=1.6.0"
dask = {extras = ["complete"], version = "^2025.2.0"}
ratelimit = "^2.2.1"
tenacity = "^8.0.0"
argparse = "^1.4.0"
tqdm = "^4.67.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.2.0"
pytest-asyncio = "^0.25.3"
pytest-mock = "^3.14.0"
pytest-xdist = "^3.5.0"
pytest-timeout = "^2.2.0"
black = "^24.2.0"
pylint = "^3.0.3"
mypy = "^1.8.0"
flake8 = "^7.0.0"
pytest-cov = "^4.1.0"
mkdocs-material = "^9.5.12"
mkdocs = "*"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
addopts = """
    -v
    -n auto
    --durations=10
    --durations-min=1.0
    --timeout=300
    --maxprocesses=4
    """
markers = [
    "quick: marks tests as quick (deselect with '-m \"not quick\"')",
    "integration: marks tests as integration tests",
]
filterwarnings = [
    "ignore::FutureWarning",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.poetry.scripts]
collect = "bourse_pipeline.scripts.collect_market_data:main"
