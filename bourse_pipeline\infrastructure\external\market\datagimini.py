import os
import sys
import asyncio
import time
import random
import pandas as pd
import finpy_tse as fpy
import argparse
import logging
from concurrent.futures import ThreadPoolExecutor
from asyncio import Semaphore
from datetime import datetime

# تنظیم event loop مخصوص ویندوز
if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# ایجاد پوشه data جهت ذخیره فایل‌های خروجی (در صورت عدم وجود)
os.makedirs("data", exist_ok=True)

# تنظیمات لاگینگ
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='data_downloader.log')
logger = logging.getLogger(__name__)

MAX_CONCURRENT_REQUESTS = 10
semaphore = Semaphore(MAX_CONCURRENT_REQUESTS)

async def download_with_rate_limit(func, *args, **kwargs):
    async with semaphore:
        return await func(*args, **kwargs)

def get_all_symbols():
    """
    دریافت لیست تمام سهام بازار بورس ایران
    """
    try:
        logger.info("در حال دریافت لیست تمام سهام از بازار ...")
        # دریافت لیست جامع سهام با استفاده از Build_Market_StockList
        stock_list = fpy.Build_Market_StockList(
            bourse=True,
            farabourse=True,
            payeh=True,
            detailed_list=True,
            show_progress=False,
            save_csv=False
        )
        logger.info(f"ستون‌های موجود در لیست سهام: {stock_list.columns.tolist()}")
        # تعیین ستون مناسب برای نماد
        symbol_col = None
        if 'نماد' in stock_list.columns:
            symbol_col = 'نماد'
        elif 'Name' in stock_list.columns:
            symbol_col = 'Name'
        elif 'Ticker(4)' in stock_list.columns:
            symbol_col = 'Ticker(4)'
        else:
            logger.warning("ستون نماد در لیست سهام یافت نشد. استفاده از اولین ستون.")
            symbol_col = stock_list.columns[0]

        if symbol_col is not None:
            symbols = stock_list[symbol_col].tolist()
            logger.info(f"{len(symbols)} نماد دریافت شد.")
            return symbols
        else:
            logger.error("نتوانستم ستون نماد را در لیست سهام پیدا کنم.")
            return
    except Exception as ex:
        logger.error(f"خطا در دریافت لیست نمادها: {ex}")
        return

async def download_daily_price_history(stock, start_date, end_date):
    """
    دریافت تاریخچه قیمت‌های روزانه یک سهم
    """
    file_path = f"data/daily_price_{stock}.csv"
    if os.path.exists(file_path):
        logger.info(f"فایل تاریخچه قیمت روزانه برای {stock} موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info(f"دریافت تاریخچه قیمت روزانه برای {stock} (تلاش {attempt + 1})...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_Price_History(
                stock=stock,
                start_date=start_date,
                end_date=end_date,
                ignore_date=False,
                adjust_price=True,
                show_weekday=False,
                double_date=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info(f"✅ تاریخچه قیمت {stock} در {file_path} ذخیره شد.")
                return
            else:
                logger.warning(f"⚠️ تاریخچه قیمت {stock} دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت تاریخچه قیمت {stock} (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم تاریخچه قیمت {stock} را پس از {retries} بار تلاش دریافت کنم.")

async def download_ri_history(stock, start_date, end_date):
    """
    دریافت اطلاعات حقیقی-حقوقی یک سهم
    """
    file_path = f"data/ri_history_{stock}.csv"
    if os.path.exists(file_path):
        logger.info(f"فایل اطلاعات حقیقی-حقوقی برای {stock} موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info(f"دریافت اطلاعات حقیقی-حقوقی برای {stock} (تلاش {attempt + 1})...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_RI_History(
                stock=stock,
                start_date=start_date,
                end_date=end_date,
                ignore_date=False,
                show_weekday=False,
                double_date=False,
                alt=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info(f"✅ اطلاعات حقیقی-حقوقی {stock} در {file_path} ذخیره شد.")
                return
            else:
                logger.warning(f"⚠️ اطلاعات حقیقی-حقوقی {stock} دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت اطلاعات حقیقی-حقوقی {stock} (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم اطلاعات حقیقی-حقوقی {stock} را پس از {retries} بار تلاش دریافت کنم.")

async def download_intraday_trades(stock, start_date, end_date):
    """
    دریافت ریز معاملات (Intraday Trades) یک سهم
    """
    file_path = f"data/intraday_trades_{stock}.csv"
    if os.path.exists(file_path):
        logger.info(f"فایل ریز معاملات برای {stock} موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info(f"دریافت ریز معاملات (Intraday Trades) برای {stock} (تلاش {attempt + 1})...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_IntradayTrades_History(
                stock=stock,
                start_date=start_date,
                end_date=end_date,
                jalali_date=True,
                combined_datatime=False,
                show_progress=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info(f"✅ ریز معاملات {stock} در {file_path} ذخیره شد.")
                return
            else:
                logger.warning(f"⚠️ ریز معاملات {stock} دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت ریز معاملات {stock} (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم ریز معاملات {stock} را پس از {retries} بار تلاش دریافت کنم.")

async def download_intraday_orderbook(stock, date):
    """
    دریافت عمق بازار (Order Book) یک سهم برای تاریخ مشخص
    """
    file_path = f"data/intraday_orderbook_{stock}_{date}.csv"
    if os.path.exists(file_path):
        logger.info(f"فایل عمق بازار برای {stock} در تاریخ {date} موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info(f"دریافت عمق بازار (Order Book) برای {stock} در تاریخ {date} (تلاش {attempt + 1})...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_IntradayOB_History(
                stock=stock,
                start_date=date,
                end_date=date,
                jalali_date=True,
                combined_datatime=False,
                show_progress=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info(f"✅ عمق بازار {stock} در {file_path} ذخیره شد.")
                return
            else:
                logger.warning(f"⚠️ عمق بازار {stock} دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت عمق بازار {stock} در تاریخ {date} (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم عمق بازار {stock} در تاریخ {date} را پس از {retries} بار تلاش دریافت کنم.")

async def download_queue_history(stock, start_date, end_date):
    """
    دریافت وضعیت صف معاملات یک سهم
    """
    file_path = f"data/queue_history_{stock}.csv"
    if os.path.exists(file_path):
        logger.info(f"فایل وضعیت صف معاملات برای {stock} موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info(f"دریافت وضعیت صف معاملات برای {stock} (تلاش {attempt + 1})...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_Queue_History(
                stock=stock,
                start_date=start_date,
                end_date=end_date,
                show_per_capita=True,
                show_weekday=False,
                double_date=False,
                show_progress=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info(f"✅ وضعیت صف معاملات {stock} در {file_path} ذخیره شد.")
                return
            else:
                logger.warning(f"⚠️ وضعیت صف معاملات {stock} دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت وضعیت صف معاملات {stock} (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم وضعیت صف معاملات {stock} را پس از {retries} بار تلاش دریافت کنم.")

async def download_market_indexes(start_date, end_date):
    """
    دریافت شاخص کل بازار (CWI)
    """
    file_path = "data/market_indexes.csv"
    if os.path.exists(file_path):
        logger.info("فایل شاخص کل بازار موجود است. دانلود انجام نشد.")
        return

    retries = 3
    for attempt in range(retries):
        try:
            logger.info("دریافت شاخص کل بازار (CWI) ...")
            await asyncio.sleep(random.uniform(1, 3))
            df = fpy.Get_CWI_History(
                start_date=start_date,
                end_date=end_date,
                ignore_date=False,
                just_adj_close=False,
                show_weekday=False,
                double_date=False
            )
            if df is not None and not df.empty:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                logger.info("✅ شاخص کل بازار در data/market_indexes.csv ذخیره شد.")
                return
            else:
                logger.warning("⚠️ شاخص کل بازار دریافت نشد.")
                return
        except Exception as ex:
            logger.error(f"❌ خطا در دریافت شاخص کل بازار (تلاش {attempt + 1}): {ex}")
            if attempt < retries - 1:
                await asyncio.sleep(5)  # صبر قبل از تلاش مجدد
    logger.error(f"❌ نتوانستم شاخص کل بازار را پس از {retries} بار تلاش دریافت کنم.")

async def download_data_for_symbol(stock, start_date, end_date, orderbook_date, data_types):
    """
    دانلود انواع داده برای یک نماد به صورت همزمان
    """
    tasks = []
    if 'daily' in data_types:
        tasks.append(download_with_rate_limit(
            download_daily_price_history, stock, start_date, end_date))
    if 'ri' in data_types:
        tasks.append(download_with_rate_limit(
            download_ri_history, stock, start_date, end_date))
    if 'intraday_trades' in data_types:
        tasks.append(download_with_rate_limit(
            download_intraday_trades, stock, start_date, end_date))
    if 'orderbook' in data_types:
        tasks.append(download_with_rate_limit(
            download_intraday_orderbook, stock, orderbook_date))
    if 'queue' in data_types:
        tasks.append(download_with_rate_limit(
            download_queue_history, stock, start_date, end_date))

    await asyncio.gather(*tasks)
    logger.info(f"---------- پایان پردازش ناهمگام برای {stock} ----------\n")

def validate_date(date_str):
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def cleanup_old_files(data_dir="data", days_old=30):
    """Remove files older than specified days"""
    import time
    current_time = time.time()
    
    for root, _, files in os.walk(data_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.exists(file_path):
                if os.stat(file_path).st_mtime < current_time - days_old * 86400:
                    try:
                        os.remove(file_path)
                        logger.info(f"Removed old file: {file_path}")
                    except Exception as e:
                        logger.error(f"Error removing {file_path}: {e}")

def main():
    parser = argparse.ArgumentParser(description="دانلود کننده داده های بازار بورس تهران.")
    parser.add_argument("--start_date", default='1400-01-01', help="تاریخ شروع (فرمت: YYYY-MM-DD)")
    parser.add_argument("--end_date", default='1401-01-01', help="تاریخ پایان (فرمت: YYYY-MM-DD)")
    parser.add_argument("--orderbook_date", default='1400-08-01', help="تاریخ عمق بازار (فرمت: YYYY-MM-DD)")
    parser.add_argument("--data_types", default='daily,ri,intraday_trades,orderbook,queue',
                        help="انواع داده برای دانلود (جدا شده با کاما: daily,ri,intraday_trades,orderbook,queue)")
    parser.add_argument("--parallel_symbols", type=int, default=5, help="تعداد نمادهایی که به صورت موازی پردازش می شوند.")
    parser.add_argument("--cleanup", type=int, help="Remove files older than specified days")

    args = parser.parse_args()

    start_date = args.start_date
    end_date = args.end_date
    orderbook_date = args.orderbook_date
    data_types = [dtype.strip() for dtype in args.data_types.split(',')]
    parallel_symbols = args.parallel_symbols

    # Validate dates
    if not all(validate_date(date) for date in [start_date, end_date, orderbook_date]):
        logger.error("Invalid date format. Please use YYYY-MM-DD format.")
        return
        
    if start_date > end_date:
        logger.error("Start date must be before end date.")
        return

    logger.info(f"تنظیمات دانلود: تاریخ شروع={start_date}, تاریخ پایان={end_date}, تاریخ عمق بازار={orderbook_date}, انواع داده={data_types}, پردازش موازی نمادها={parallel_symbols}")

    if args.cleanup:
        cleanup_old_files(days_old=args.cleanup)

    # دریافت لیست تمام نمادها
    symbols = get_all_symbols()
    if not symbols:
        logger.warning("لیست نمادها دریافت نشد. اجرای برنامه متوقف می‌شود.")
        return

    total = len(symbols)
    logger.info(f"تعداد کل نمادها برای پردازش: {total}")

    async def process_symbols(symbols_chunk):
        try:
            tasks = [download_data_for_symbol(stock, start_date, end_date, orderbook_date, data_types) 
                    for stock in symbols_chunk]
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error processing symbols chunk: {e}")

    # تقسیم لیست نمادها به دسته‌های کوچکتر برای پردازش موازی
    chunks = [symbols[i:i + parallel_symbols] for i in range(0, len(symbols), parallel_symbols)]

    logger.info("شروع پردازش موازی نمادها...")
    for idx, chunk in enumerate(chunks, start=1):
        logger.info(f"\n========== پردازش گروه {idx}/{len(chunks)} (شامل نمادهای {chunk}) ==========")
        asyncio.run(process_symbols(chunk))

    # دریافت شاخص کل بازار به‌عنوان داده کلی
    asyncio.run(download_market_indexes(start_date, end_date))
    logger.info("\n🎯 تمام داده‌ها دریافت و در پوشه 'data' ذخیره شدند.")
    logger.info("جزئیات بیشتر در فایل 'data_downloader.log' قابل مشاهده است.")

if __name__ == "__main__":
    main()

# پیشنهاد برای بهبودهای آینده:
# 1. ادغام داده های مختلف برای یک نماد در صورت نیاز.
# 2. پیاده سازی قابلیت به روز رسانی داده ها به صورت دوره ای.
