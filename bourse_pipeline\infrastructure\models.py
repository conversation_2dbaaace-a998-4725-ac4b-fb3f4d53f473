from sqlalchemy import Column, Integer, String, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import JSONB

Base = declarative_base()

class Alert(Base):
    __tablename__ = 'alerts'
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, nullable=False)
    severity = Column(String, nullable=False)
    message = Column(String, nullable=False)
    source = Column(String)
    is_resolved = Column(Boolean)
    resolved_at = Column(DateTime)
    resolution_note = Column(String)

class SystemMetric(Base):
    __tablename__ = 'system_metrics'
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, nullable=False)
    cpu_usage = Column(Float)
    memory_usage = Column(Float)
    disk_usage = Column(Float)
    network_latency = Column(Float)
    active_connections = Column(Integer)
    error_count = Column(Integer)
    status = Column(String, nullable=False)

class MarketUnifiedData(Base):
    __tablename__ = 'market_unified_data'

    id = Column(Integer, primary_key=True)
    symbol = Column(String(50), nullable=False)
    raw_data = Column(JSONB, nullable=False)
    extraction_date = Column(DateTime, nullable=False)

