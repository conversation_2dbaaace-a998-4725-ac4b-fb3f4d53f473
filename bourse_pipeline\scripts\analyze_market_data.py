import logging
import sys
import os
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import pandas as pd
import numpy as np
import json
from dotenv import load_dotenv

# بارگذاری متغیرهای محیطی از فایل .env
load_dotenv()

# تنظیم مسیر برای واردسازی ماژول‌های پروژه
sys.path.append(str(Path(__file__).parent.parent.parent))

from bourse_pipeline.infrastructure.database.enhanced_market_storage import EnhancedMarketStorage

# تنظیم لاگر
log_path = Path('logs')
log_path.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_path / 'market_data_analysis.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """پردازش آرگومان‌های ورودی برنامه"""
    parser = argparse.ArgumentParser(description="تحلیل داده‌های بازار سهام تهران")
    parser.add_argument(
        "--symbol",
        help="نماد مورد نظر (اگر مشخص نشود، همه نمادها تحلیل می‌شوند)",
        type=str,
        default=None
    )
    parser.add_argument(
        "--start-date",
        help="تاریخ شروع تحلیل داده‌ها (YYYY-MM-DD)",
        type=str,
        default=(datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--end-date",
        help="تاریخ پایان تحلیل داده‌ها (YYYY-MM-DD)",
        type=str,
        default=datetime.now().strftime('%Y-%m-%d')
    )
    parser.add_argument(
        "--output-dir",
        help="مسیر ذخیره نتایج تحلیل",
        type=str,
        default="output/analysis"
    )
    parser.add_argument(
        "--db-url",
        help="آدرس اتصال به دیتابیس",
        type=str,
        default=os.getenv("DATABASE_URL")
    )
    
    return parser.parse_args()

def validate_date(date_str: str) -> bool:
    """اعتبارسنجی فرمت تاریخ"""
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def ensure_directory(path: str) -> Path:
    """اطمینان از وجود دایرکتوری و ایجاد آن در صورت نیاز"""
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path

def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    محاسبه شاخص‌های تکنیکال برای یک DataFrame
    
    Args:
        df: DataFrame حاوی داده‌های قیمت (باید ستون‌های date, open, high, low, close, volume را داشته باشد)
        
    Returns:
        DataFrame با شاخص‌های تکنیکال اضافه شده
    """
    # اطمینان از مرتب بودن داده‌ها بر اساس تاریخ
    df = df.sort_values('date')
    
    # کپی از DataFrame اصلی
    result = df.copy()
    
    # میانگین متحرک ساده (SMA)
    result['sma_20'] = result['close'].rolling(window=20).mean()
    result['sma_50'] = result['close'].rolling(window=50).mean()
    result['sma_200'] = result['close'].rolling(window=200).mean()
    
    # میانگین متحرک نمایی (EMA)
    result['ema_20'] = result['close'].ewm(span=20, adjust=False).mean()
    result['ema_50'] = result['close'].ewm(span=50, adjust=False).mean()
    
    # شاخص قدرت نسبی (RSI)
    delta = result['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()
    rs = avg_gain / avg_loss
    result['rsi_14'] = 100 - (100 / (1 + rs))
    
    # MACD (Moving Average Convergence Divergence)
    result['ema_12'] = result['close'].ewm(span=12, adjust=False).mean()
    result['ema_26'] = result['close'].ewm(span=26, adjust=False).mean()
    result['macd'] = result['ema_12'] - result['ema_26']
    result['macd_signal'] = result['macd'].ewm(span=9, adjust=False).mean()
    result['macd_hist'] = result['macd'] - result['macd_signal']
    
    # باندهای بولینگر (Bollinger Bands)
    result['bb_middle'] = result['close'].rolling(window=20).mean()
    result['bb_std'] = result['close'].rolling(window=20).std()
    result['bb_upper'] = result['bb_middle'] + (result['bb_std'] * 2)
    result['bb_lower'] = result['bb_middle'] - (result['bb_std'] * 2)
    
    # میانگین دامنه واقعی (ATR)
    high_low = result['high'] - result['low']
    high_close = (result['high'] - result['close'].shift()).abs()
    low_close = (result['low'] - result['close'].shift()).abs()
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    result['atr_14'] = true_range.rolling(14).mean()
    
    # حجم تجمعی (OBV - On-Balance Volume)
    result['obv'] = np.where(
        result['close'] > result['close'].shift(1),
        result['volume'],
        np.where(
            result['close'] < result['close'].shift(1),
            -result['volume'],
            0
        )
    ).cumsum()
    
    return result

def generate_trading_signals(df: pd.DataFrame) -> pd.DataFrame:
    """
    تولید سیگنال‌های معاملاتی بر اساس شاخص‌های تکنیکال
    
    Args:
        df: DataFrame حاوی داده‌های قیمت و شاخص‌های تکنیکال
        
    Returns:
        DataFrame با سیگنال‌های معاملاتی اضافه شده
    """
    # کپی از DataFrame اصلی
    result = df.copy()
    
    # سیگنال‌های میانگین متحرک
    result['signal_ma_cross'] = 0
    result.loc[result['sma_20'] > result['sma_50'], 'signal_ma_cross'] = 1
    result.loc[result['sma_20'] < result['sma_50'], 'signal_ma_cross'] = -1
    
    # سیگنال‌های RSI
    result['signal_rsi'] = 0
    result.loc[result['rsi_14'] < 30, 'signal_rsi'] = 1  # خرید (اشباع فروش)
    result.loc[result['rsi_14'] > 70, 'signal_rsi'] = -1  # فروش (اشباع خرید)
    
    # سیگنال‌های MACD
    result['signal_macd'] = 0
    result.loc[result['macd'] > result['macd_signal'], 'signal_macd'] = 1
    result.loc[result['macd'] < result['macd_signal'], 'signal_macd'] = -1
    
    # سیگنال‌های باندهای بولینگر
    result['signal_bb'] = 0
    result.loc[result['close'] < result['bb_lower'], 'signal_bb'] = 1  # خرید (قیمت زیر باند پایین)
    result.loc[result['close'] > result['bb_upper'], 'signal_bb'] = -1  # فروش (قیمت بالای باند بالا)
    
    # سیگنال ترکیبی
    result['signal_combined'] = (
        result['signal_ma_cross'] + 
        result['signal_rsi'] + 
        result['signal_macd'] + 
        result['signal_bb']
    )
    
    # تبدیل سیگنال ترکیبی به سیگنال نهایی
    result['signal'] = 0
    result.loc[result['signal_combined'] >= 2, 'signal'] = 1  # سیگنال خرید قوی
    result.loc[result['signal_combined'] <= -2, 'signal'] = -1  # سیگنال فروش قوی
    
    return result

def analyze_market_data(
    symbol: Optional[str],
    start_date: str,
    end_date: str,
    output_dir: str,
    db_url: str
) -> bool:
    """
    تحلیل داده‌های بازار و تولید سیگنال‌های معاملاتی
    
    Args:
        symbol: نماد مورد نظر (اگر None باشد، همه نمادها تحلیل می‌شوند)
        start_date: تاریخ شروع تحلیل
        end_date: تاریخ پایان تحلیل
        output_dir: مسیر ذخیره نتایج تحلیل
        db_url: آدرس اتصال به دیتابیس
    """
    try:
        # ایجاد مسیر خروجی
        output_path = ensure_directory(output_dir)
        
        # اتصال به پایگاه داده
        storage = EnhancedMarketStorage(db_url)
        
        # دریافت داده‌ها از پایگاه داده
        df = storage.get_market_data(symbol, start_date, end_date)
        
        if df.empty:
            logger.error(f"❌ هیچ داده‌ای برای تحلیل یافت نشد (نماد: {symbol}, تاریخ: {start_date} تا {end_date})")
            return False
            
        logger.info(f"✅ {len(df)} رکورد داده از پایگاه داده دریافت شد")
        
        # گروه‌بندی داده‌ها بر اساس نماد
        symbols_data = {}
        for sym, group in df.groupby('symbol'):
            symbols_data[sym] = group
            
        logger.info(f"✅ داده‌های {len(symbols_data)} نماد برای تحلیل آماده شد")
        
        # تحلیل داده‌ها و تولید سیگنال‌های معاملاتی
        analysis_results = {}
        signals_summary = {}
        
        for sym, data in symbols_data.items():
            # محاسبه شاخص‌های تکنیکال
            with_indicators = calculate_technical_indicators(data)
            
            # تولید سیگنال‌های معاملاتی
            with_signals = generate_trading_signals(with_indicators)
            
            # ذخیره نتایج
            analysis_results[sym] = with_signals
            
            # خلاصه سیگنال‌های اخیر
            latest_data = with_signals.iloc[-5:].copy()  # 5 روز آخر
            buy_signals = latest_data[latest_data['signal'] == 1]
            sell_signals = latest_data[latest_data['signal'] == -1]
            
            signals_summary[sym] = {
                'latest_close': latest_data['close'].iloc[-1] if not latest_data.empty else None,
                'latest_date': latest_data['date'].iloc[-1].strftime('%Y-%m-%d') if not latest_data.empty else None,
                'recent_buy_signals': len(buy_signals),
                'recent_sell_signals': len(sell_signals),
                'latest_signal': latest_data['signal'].iloc[-1] if not latest_data.empty else 0,
                'rsi_14': latest_data['rsi_14'].iloc[-1] if not latest_data.empty else None,
                'sma_20': latest_data['sma_20'].iloc[-1] if not latest_data.empty else None,
                'sma_50': latest_data['sma_50'].iloc[-1] if not latest_data.empty else None
            }
        
        # ذخیره نتایج تحلیل
        for sym, data in analysis_results.items():
            # ذخیره به صورت CSV
            output_file = output_path / f"{sym}_analysis.csv"
            data.to_csv(output_file, index=False, encoding='utf-8-sig')
            
        # ذخیره خلاصه سیگنال‌ها
        summary_file = output_path / "signals_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(signals_summary, f, ensure_ascii=False, indent=4)
            
        logger.info(f"✅ نتایج تحلیل {len(analysis_results)} نماد در مسیر {output_path} ذخیره شد")
        
        # نمایش خلاصه سیگنال‌های خرید و فروش
        buy_signals = {sym: info for sym, info in signals_summary.items() if info['latest_signal'] == 1}
        sell_signals = {sym: info for sym, info in signals_summary.items() if info['latest_signal'] == -1}
        
        logger.info(f"\n📊 خلاصه سیگنال‌های معاملاتی:")
        logger.info(f"  - تعداد سیگنال‌های خرید: {len(buy_signals)}")
        logger.info(f"  - تعداد سیگنال‌های فروش: {len(sell_signals)}")
        
        if buy_signals:
            logger.info("\n🟢 سیگنال‌های خرید:")
            for sym, info in buy_signals.items():
                logger.info(f"  - {sym}: قیمت آخرین معامله: {info['latest_close']}, تاریخ: {info['latest_date']}, RSI: {info['rsi_14']:.2f}")
                
        if sell_signals:
            logger.info("\n🔴 سیگنال‌های فروش:")
            for sym, info in sell_signals.items():
                logger.info(f"  - {sym}: قیمت آخرین معامله: {info['latest_close']}, تاریخ: {info['latest_date']}, RSI: {info['rsi_14']:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطا در تحلیل داده‌ها: {str(e)}")
        logger.exception("جزئیات خطا:")
        return False

def main():
    """تابع اصلی برنامه"""
    args = parse_args()

    # اعتبارسنجی تاریخ‌ها
    if not all(validate_date(date) for date in [args.start_date, args.end_date]):
        logger.error("❌ فرمت تاریخ نامعتبر. لطفا از فرمت YYYY-MM-DD استفاده کنید")
        sys.exit(1)

    # اعتبارسنجی آدرس دیتابیس
    if not args.db_url:
        logger.error("❌ آدرس دیتابیس مشخص نشده است. لطفا با پارامتر --db-url آدرس دیتابیس را مشخص کنید یا در فایل .env تنظیم کنید")
        sys.exit(1)

    # تحلیل داده‌ها
    success = analyze_market_data(
        symbol=args.symbol,
        start_date=args.start_date,
        end_date=args.end_date,
        output_dir=args.output_dir,
        db_url=args.db_url
    )

    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
