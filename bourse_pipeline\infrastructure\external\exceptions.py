class ExternalServiceError(Exception):
    """خطای عمومی سرویس‌های خارجی"""
    pass

class MarketDataError(ExternalServiceError):
    """خطای دریافت داده‌های بازار"""
    pass

class EmptyDataError(MarketDataError):
    """خطای داده خالی"""
    pass

class ServiceConnectionError(ExternalServiceError):
    """خطای اتصال به سرویس"""
    pass

class ServiceTimeoutError(ExternalServiceError):
    """خطای تایم‌اوت سرویس"""
    pass

class InvalidSymbolError(MarketDataError):
    """خطای نماد نامعتبر"""
    pass

class DateRangeError(MarketDataError):
    """خطای بازه زمانی نامعتبر"""
    pass
