from datetime import datetime, date
from typing import Union, <PERSON><PERSON>

def convert_date(
    date_str: str,
    output_format: str = "tse"
) -> str:
    """
    تبدیل تاریخ به فرمت‌های مختلف
    
    Args:
        date_str: تاریخ ورودی به فرمت YYYY-MM-DD
        output_format: فرمت خروجی ("tse", "standard")
    
    Returns:
        str: تاریخ تبدیل شده
    """
    if output_format == "tse":
        return date_str.replace("-", "")
    return date_str

def get_date_range(
    start_date: str,
    end_date: str,
    output_format: str = "tse"
) -> Tuple[str, str]:
    """
    تبدیل بازه تاریخی به فرمت مورد نظر
    """
    start = convert_date(start_date, output_format)
    end = convert_date(end_date, output_format)
    return start, end