import finpy_tse as tse
import pandas as pd
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import sys
import codecs
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

warnings.filterwarnings('ignore', category=FutureWarning, module='finpy_tse')

logger = logging.getLogger(__name__)

class MarketDataCollector:
    def __init__(self, request_delay: float = 0.5, max_retries: int = 3, max_workers: int = 4):
        self.request_delay = request_delay
        self.max_retries = max_retries
        self.max_workers = max_workers
        self.all_stock_data: Dict[str, pd.DataFrame] = {}

    def get_raw_market_data(self, start_date: str, end_date: str) -> Dict[str, Dict]:
        """
        دریافت تمام داده‌های خام مورد نیاز برای یادگیری ماشین
        """
        try:
            logger.info("شروع جمع‌آوری داده‌های خام بازار...")

            raw_data = {
                'stock_prices': self.collect_all_price_data(start_date, end_date),
                'market_indices': self.get_market_indices(start_date, end_date),
                'trading_volume': self.get_trading_volumes(start_date, end_date),
                'market_stats': self.get_market_statistics(start_date, end_date),
                'stock_info': self.get_stocks_information()
            }

            logger.info("✅ جمع‌آوری داده‌های خام با موفقیت انجام شد")
            return raw_data

        except Exception as e:
            logger.error(f"❌ خطا در جمع‌آوری داده‌های خام: {str(e)}")
            raise

    def collect_all_price_data(self, start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Collect price data for all stocks using parallel processing"""
        tickers = self.get_market_stocks()
        if not tickers:
            return {}

        price_data = {}
        failed_tickers = []  # Initialize as empty list

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_ticker = {
                executor.submit(self.get_stock_history, ticker, start_date, end_date): ticker
                for ticker in tickers
            }

            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Close_Adj']
                        available_columns = [col for col in required_columns if col in df.columns]
                        price_data[ticker] = df[available_columns].copy()
                    else:
                        failed_tickers.append(ticker)
                except Exception as e:
                    logger.error(f"❌ Error collecting data for {ticker}: {str(e)}")
                    failed_tickers.append(ticker)

        if failed_tickers:
            logger.warning(f"Failed to collect data for {len(failed_tickers)} tickers: {', '.join(failed_tickers)}")

        return price_data

    def get_market_indices(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        دریافت شاخص‌های اصلی بازار
        شامل: شاخص کل، هم‌وزن و فرابورس
        """
        logger.info("در حال دریافت شاخص‌های اصلی بازار...")
        try:
            indices = tse.Get_MarketWatch()
            if isinstance(indices, tuple):
                indices = indices[0]
            logger.info("✅ دریافت شاخص‌های اصلی بازار با موفقیت انجام شد.")
            return indices
        except Exception as e:
            logger.error(f"❌ خطا در دریافت شاخص‌های بازار: {str(e)}")
            return None

    def get_trading_volumes(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        دریافت حجم و ارزش معاملات کل بازار
        """
        logger.info("در حال دریافت حجم و ارزش معاملات کل بازار...")
        try:
            market_stats = tse.Get_MarketWatch()
            if isinstance(market_stats, tuple):
                market_stats = market_stats[0]
            columns = ['Volume', 'Value'] if all(col in market_stats.columns for col in ['Volume', 'Value']) else market_stats.columns[:2]
            logger.info("✅ دریافت حجم و ارزش معاملات کل بازار با موفقیت انجام شد.")
            return market_stats[columns]
        except Exception as e:
            logger.error(f"❌ خطا در دریافت حجم معاملات: {str(e)}")
            return None

    def get_market_statistics(self, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        دریافت آمار کلی بازار
        شامل: تعداد معاملات، ارزش بازار، تعداد خریداران و فروشندگان
        """
        logger.info("در حال دریافت آمار کلی بازار...")
        try:
            stats = tse.Get_MarketWatch()
            if isinstance(stats, tuple):
                stats = stats[0]
            logger.info("✅ دریافت آمار کلی بازار با موفقیت انجام شد.")
            return stats
        except Exception as e:
            logger.error(f"❌ خطا در دریافت آمار بازار: {str(e)}")
            return None

    def get_stocks_information(self) -> Optional[pd.DataFrame]:
        """
        دریافت اطلاعات پایه‌ای سهام
        """
        logger.info("در حال دریافت اطلاعات پایه‌ای سهام...")
        try:
            stocks_info = tse.Build_Market_StockList(
                bourse=True,
                farabourse=True,
                payeh=True,
                detailed_list=False
            )
            logger.info("✅ دریافت اطلاعات پایه‌ای سهام با موفقیت انجام شد.")
            return stocks_info
        except Exception as e:
            logger.error(f"❌ خطا در دریافت اطلاعات سهام: {str(e)}")
            return None

    def get_market_stocks(self) -> Optional[List[str]]:
        """دریافت لیست سهام بازار"""
        logger.info("در حال دریافت لیست سهام بازار...")
        try:
            stock_list = tse.Build_Market_StockList(
                bourse=True,
                farabourse=True,
                payeh=True,
                detailed_list=False,
                show_progress=False,
                save_excel=False,
                save_csv=False
            )

            if stock_list is None or stock_list.empty:
                logger.error("❌ لیست سهام دریافت شده خالی است")
                return None

            logger.info("✅ لیست سهام بازار با موفقیت دریافت شد")
            logger.debug(f"نمونه داده لیست سهام:\n{stock_list.head()}")

            tickers = stock_list.index.to_list()
            logger.info(f"📊 تعداد کل سهام: {len(tickers):,}")
            return tickers

        except Exception as e:
            logger.error(f"❌ خطا در دریافت لیست سهام: {str(e)}")
            return None

    def get_stock_history(self, ticker: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """Get historical data for a single stock with retry mechanism"""
        logger.info(f"در حال دریافت تاریخچه قیمت برای نماد {ticker}...")
        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    time.sleep(self.request_delay * (2 ** attempt))

                df = tse.Get_Price_History(
                    stock=ticker,
                    start_date=start_date,
                    end_date=end_date,
                    ignore_date=False,
                    adjust_price=True,
                    show_weekday=True,
                    double_date=True
                )

                if isinstance(df, tuple):
                    df = df[0]

                if df is not None and not df.empty:
                    try:
                        if 'Date' in df.columns:
                            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
                            if df['Date'].isna().any():
                                df['Date'] = pd.to_datetime(df['Date'].astype(str).str.strip(), format='%Y-%m-%d', errors='coerce')
                    except Exception as date_error:
                        logger.warning(f"Date conversion warning for {ticker}: {str(date_error)}")
                        df = df.dropna(subset=['Date'])

                    if 'Close_Adj' not in df.columns:
                        df['Close_Adj'] = df['Adj Close'] if 'Adj Close' in df.columns else df['Close']
                    logger.info(f"✅ دریافت تاریخچه قیمت برای نماد {ticker} با موفقیت انجام شد.")
                    return df

            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"❌ Failed to get history for {ticker} after {self.max_retries} attempts: {str(e)}")
                    return None
                logger.warning(f"⚠️ Error getting history for {ticker} (attempt {attempt + 1}): {str(e)}")

            time.sleep(self.request_delay)
        return None

    def collect_all_data(self, start_date: str, end_date: str, batch_size: int = 50) -> Dict[str, pd.DataFrame]:
        """جمع‌آوری داده‌های همه سهام با پردازش دسته‌ای و نمایش نوار پیشرفت"""
        tickers = self.get_market_stocks()
        if not tickers:
            return {}

        total_batches = (len(tickers) + batch_size - 1) // batch_size
        successful_count = 0
        failed_tickers = []      # Initialize as empty list
        empty_data_tickers = []  # Initialize as empty list

        for batch_idx in range(total_batches):
            batch_start = batch_idx * batch_size
            batch_end = min(batch_start + batch_size, len(tickers))
            batch_tickers = tickers[batch_start:batch_end]

            logger.info(f"\n📦 پردازش دسته {batch_idx + 1} از {total_batches}")

            for ticker in tqdm(batch_tickers, desc=f"دسته {batch_idx + 1}/{total_batches}", leave=False):
                df = self.get_stock_history(ticker, start_date, end_date)
                if df is not None:
                    if df.empty:
                        empty_data_tickers.append(ticker)
                    else:
                        self.all_stock_data[ticker] = df
                        successful_count += 1
                else:
                    failed_tickers.append(ticker)

        logger.info("\n📊 خلاصه عملیات:")
        logger.info(f"  - تعداد کل نمادها: {len(tickers):,}")
        logger.info(f"  - موفق: {successful_count:,}")
        logger.info(f"  - ناموفق: {len(failed_tickers):,}")
        logger.info(f"  - داده خالی: {len(empty_data_tickers):,}")

        if empty_data_tickers:
            logger.warning("\n⚠️ نمادهای با داده خالی:")
            logger.warning(f"  {', '.join(empty_data_tickers)}")

        if failed_tickers:
            logger.warning("\n⚠️ نمادهای ناموفق:")
            logger.warning(f"  {', '.join(failed_tickers)}")

        return self.all_stock_data

    def save_data(self, raw_data: Dict[str, Dict], output_dir: str = 'output'):
        """ذخیره داده‌های خام در فایل‌های JSON"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        for data_type, data in raw_data.items():
            file_path = os.path.join(output_dir, f'{data_type}.json')
            try:
                if isinstance(data, dict):
                    # Convert pandas DataFrames within the dictionary to lists of dictionaries
                    serializable_data = {
                        key: value.to_dict(orient='records') if isinstance(value, pd.DataFrame) else value
                        for key, value in data.items()
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(serializable_data, f, ensure_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های نوع '{data_type}' در '{file_path}' ذخیره شد.")
                elif isinstance(data, pd.DataFrame):
                    data.to_json(file_path, orient='records', force_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های نوع '{data_type}' در '{file_path}' ذخیره شد.")
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    logger.info(f"✅ داده‌های نوع '{data_type}' در '{file_path}' ذخیره شد.")
            except Exception as e:
                logger.error(f"❌ خطا در ذخیره داده‌های نوع '{data_type}' در '{file_path}': {str(e)}")

def main():
    """تابع اصلی برنامه"""
    parser = argparse.ArgumentParser(description="جمع‌آوری داده‌های بازار سهام تهران")
    parser.add_argument("start_date", help="تاریخ شروع جمع‌آوری داده‌ها (YYYY-MM-DD)")
    parser.add_argument("end_date", help="تاریخ پایان جمع‌آوری داده‌ها (YYYY-MM-DD)")
    parser.add_argument("--output_dir", default="output", help="مسیر ذخیره داده‌های خروجی")
    args = parser.parse_args()

    try:
        start_date = args.start_date
        end_date = args.end_date
        output_dir = args.output_dir

        logger.info(f"تاریخ شروع: {start_date}, تاریخ پایان: {end_date}, مسیر خروجی: {output_dir}")

        collector = MarketDataCollector(
            request_delay=0.5,
            max_retries=3,
            max_workers=4
        )

        start_time = time.time()
        raw_data = collector.get_raw_market_data(start_date, end_date)

        logger.info("\n📊 خلاصه داده‌های دریافت شده:")
        for data_type, data in raw_data.items():
            if isinstance(data, dict):
                logger.info(f"  - {data_type}: {len(data)} نماد")
            elif isinstance(data, pd.DataFrame):
                logger.info(f"  - {data_type}: {len(data)} رکورد")
            else:
                logger.info(f"  - {data_type}: {data}")

        collector.save_data(raw_data, output_dir)

        execution_time = time.time() - start_time
        logger.info(f"\n⏱️ زمان کل اجرا: {execution_time:.2f} ثانیه")

    except Exception as e:
        logger.error(f"❌ خطای کلی برنامه: {str(e)}")
        raise

if __name__ == '__main__':
    main()
