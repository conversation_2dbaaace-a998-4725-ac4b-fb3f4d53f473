from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from .settings import DatabaseSettings

Base = declarative_base()

class Database:
    def __init__(self, settings: DatabaseSettings):
        self.settings = settings
        self.engine = None
        self.SessionLocal = None

    def initialize(self):
        """Initialize database connection"""
        self.engine = create_engine(
            self.settings.url,
            pool_pre_ping=True,
            echo=self.settings.echo
        )
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Create all tables
        Base.metadata.create_all(bind=self.engine)

    def get_session(self):
        """Get database session"""
        if self.SessionLocal is None:
            raise RuntimeError("Database not initialized. Call initialize() first.")
        return self.SessionLocal()