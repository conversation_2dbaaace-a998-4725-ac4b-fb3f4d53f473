

## تست‌ها

پروژه دارای سه نوع تست است:

### تست‌های واحد (Unit Tests)
```bash
pytest tests/test_ingestion.py
```
نتیجه: 5 passed, 2 skipped

### تست‌های یکپارچگی (Integration Tests)
```bash
pytest tests/test_ingestion.py --run-integration
```
نتیجه: 6 passed, 1 skipped

### تست‌های یکپارچگی به تنهایی
```bash
pytest tests/test_ingestion.py -m integration --run-integration
```
نتیجه: 1 passed, 1 skipped, 5 deselected

### نکات
- تست‌های یکپارچگی نیاز به اتصال به API بورس دارند
- برای اجرای تست‌های یکپارچگی از پارامتر `--run-integration` استفاده کنید
- هشدار FutureWarning از finpy_tse نادیده گرفته می‌شود